# Library Management System

A Library Management System built with Python and SQLite, featuring both console-based and graphical user interfaces.

## Features

- Admin Authentication
- Book Management (CRUD operations)
- Student Management (CRUD operations)
- Book Issue and Return Management
- Fine Calculation for Late Returns

## Requirements

- Python 3.6 or higher
- SQLite3 (included in Python standard library)

## Project Structure

- `database.py` - Database connection and initialization
- `models.py` - Data models for Admin, Book, Student, and IssuedBook
- `controllers.py` - Business logic for all operations
- `views.py` - Console-based user interface
- `main.py` - Entry point for the console application
- `gui.py` - Graphical user interface using Tkinter
- `gui_main.py` - Entry point for the GUI application

## How to Run

1. Make sure you have Python installed on your system.
2. Navigate to the project directory in your terminal.
3. Run the application with one of the following commands:

### Console Version
```
python main.py
```

### GUI Version
```
python gui_main.py
```

### Standalone Executable
You can also run the application as a standalone executable:

1. Build the executable by running:
   ```
   python build_exe.py
   ```
   or simply double-click on `build.bat`

2. Once built, find the executable in the `dist` folder
3. Double-click on `LibraryManagementSystem.exe` to run the application

The executable will use the `lib.ico` icon for the application window and taskbar.

## Default Admin Credentials

- Username: admin
- Password: admin123

## Usage Guide

### Admin Login

When you start the application, you'll be prompted to enter your admin credentials.

### Main Menu

After successful login, you'll see the main menu with the following options:

1. Book Management
2. Student Management
3. Issue/Return Books
4. Change Password
5. Logout

### Book Management

- Add new books with title, author, ISBN, and quantity
- View all books in the library
- Search for books by title, author, or ISBN
- Update book details
- Delete books (if not issued to any student)

### Student Management

- Add new students with name, roll number, department, and email
- View all registered students
- Update student information
- Delete students (if they don't have any issued books)

### Issue/Return Books

- Issue books to students with a specified due date
- Return books and calculate fines for late returns
- View all issued books with their status
- View books issued to a specific student

## Database Schema

### Admin Table
- admin_id (INTEGER PRIMARY KEY)
- username (TEXT UNIQUE NOT NULL)
- password (TEXT NOT NULL)

### Books Table
- book_id (INTEGER PRIMARY KEY)
- title (TEXT NOT NULL)
- author (TEXT NOT NULL)
- isbn (TEXT UNIQUE NOT NULL)
- quantity (INTEGER NOT NULL)

### Students Table
- student_id (INTEGER PRIMARY KEY)
- name (TEXT NOT NULL)
- roll_no (TEXT UNIQUE NOT NULL)
- department (TEXT NOT NULL)
- email (TEXT NOT NULL)

### IssuedBooks Table
- issue_id (INTEGER PRIMARY KEY)
- book_id (INTEGER NOT NULL, FOREIGN KEY)
- student_id (INTEGER NOT NULL, FOREIGN KEY)
- issue_date (TEXT NOT NULL)
- due_date (TEXT NOT NULL)
- return_date (TEXT or NULL)

## GUI Features

The graphical user interface provides the following features:

- Modern and intuitive user interface
- Dashboard with system statistics
- Book management with search functionality
- Student management
- Issue and return books with a tabbed interface
- View all issued books and their status
- View books issued to a specific student
- Change password functionality
- Custom application icon (lib.ico) in window title and taskbar
- Back to dashboard buttons for easy navigation

## Future Enhancements

- Web-based interface using Flask
- Export data to CSV or PDF
- Email notifications for overdue books
- Barcode scanning for books
- Reports and statistics
- ✅ Standalone executable with custom icon (implemented)
