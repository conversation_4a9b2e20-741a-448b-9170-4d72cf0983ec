# Building and Running the Library Management System Executable

This guide explains how to build and run the standalone executable version of the Library Management System.

## Prerequisites

- Python 3.6 or higher
- PyInstaller (will be installed automatically by the build script if not present)
- The lib.ico file in the project directory

## Building the Executable

### Method 1: Using the Batch File (Recommended)

1. Double-click on the `build.bat` file in the project directory
2. Wait for the build process to complete
3. The executable will be created in the `dist` folder

### Method 2: Using Python

Run the following command in your terminal:

```
C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe "d:/Paid Projects/lib/build_exe.py"
```

## Running the Application

### Method 1: From the Executable

1. Navigate to the `dist` folder
2. Double-click on `LibraryManagementSystem.exe`

### Method 2: From Source Code

Run the following command in your terminal:

```
C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe "d:/Paid Projects/lib/gui_main.py"
```

Or simply double-click on the `run.bat` file in the project directory.

## Troubleshooting

### Icon Issues

If the icon doesn't appear in the executable:

1. Make sure the `lib.ico` file is in the project directory
2. The icon file should be a valid .ico file
3. Try rebuilding the executable

### PyInstaller Installation Issues

If PyInstaller fails to install automatically:

1. Open a command prompt
2. Run: `C:/Users/<USER>/AppData/Local/Programs/Python/Python312/python.exe -m pip install pyinstaller`
3. Try building again

### Executable Errors

If the executable fails to run:

1. Try running the application from source code to check for any errors
2. Check if all required files are included in the build
3. Rebuild with the `--debug` option by editing the build_exe.py file

## Creating a Shortcut

To create a shortcut to the executable:

1. Right-click on `LibraryManagementSystem.exe` in the `dist` folder
2. Select "Create shortcut"
3. Move the shortcut to your desktop or preferred location

The shortcut will maintain the custom icon from the executable.
