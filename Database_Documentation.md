# Library Management System - Database Documentation

## Project Overview
This document provides comprehensive documentation of the database design, queries, and operations for the Library Management System project. The system uses SQLite as the database engine and implements a complete library management solution with admin authentication, book management, student management, and book issue/return functionality.

## Database Schema

### 1. Admin Table
Stores administrator credentials for system access.

```sql
CREATE TABLE IF NOT EXISTS Admin (
    admin_id INTEGER PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL
);
```

**Columns:**
- `admin_id`: Primary key, auto-incrementing integer
- `username`: Unique username for admin login
- `password`: Hashed password using SHA-256 encryption

### 2. Books Table
Stores information about all books in the library.

```sql
CREATE TABLE IF NOT EXISTS Books (
    book_id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    isbn TEXT UNIQUE NOT NULL,
    quantity INTEGER NOT NULL
);
```

**Columns:**
- `book_id`: Primary key, auto-incrementing integer
- `title`: Book title (required)
- `author`: Book author (required)
- `isbn`: International Standard Book Number (unique, required)
- `quantity`: Number of copies available

### 3. Students Table
Stores student information for library membership.

```sql
CREATE TABLE IF NOT EXISTS Students (
    student_id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    roll_no TEXT UNIQUE NOT NULL,
    department TEXT NOT NULL,
    email TEXT NOT NULL
);
```

**Columns:**
- `student_id`: Primary key, auto-incrementing integer
- `name`: Student's full name (required)
- `roll_no`: Unique student roll number (required)
- `department`: Student's department (required)
- `email`: Student's email address (required)

### 4. IssuedBooks Table
Tracks book lending and return operations.

```sql
CREATE TABLE IF NOT EXISTS IssuedBooks (
    issue_id INTEGER PRIMARY KEY,
    book_id INTEGER NOT NULL,
    student_id INTEGER NOT NULL,
    issue_date TEXT NOT NULL,
    due_date TEXT NOT NULL,
    return_date TEXT,
    FOREIGN KEY (book_id) REFERENCES Books (book_id),
    FOREIGN KEY (student_id) REFERENCES Students (student_id)
);
```

**Columns:**
- `issue_id`: Primary key, auto-incrementing integer
- `book_id`: Foreign key referencing Books table
- `student_id`: Foreign key referencing Students table
- `issue_date`: Date when book was issued (YYYY-MM-DD format)
- `due_date`: Date when book should be returned (YYYY-MM-DD format)
- `return_date`: Actual return date (NULL if not returned yet)

## Database Relationships

```
Admin (1) ──── (manages) ──── (many) Books
                                │
                                │
Students (many) ──── (borrows) ──── (many) Books
                │                      │
                └──── IssuedBooks ──────┘
```

## Core Database Operations

### 1. Database Initialization

#### Create Default Admin
```sql
-- Check if admin exists
SELECT COUNT(*) FROM Admin;

-- Insert default admin if none exists
INSERT INTO Admin (username, password) VALUES (?, ?);
```

### 2. Admin Operations

#### Admin Authentication
```sql
SELECT * FROM Admin WHERE username = ? AND password = ?;
```

### 3. Book Management Operations

#### Add New Book
```sql
INSERT INTO Books (title, author, isbn, quantity) VALUES (?, ?, ?, ?);
```

#### Get All Books
```sql
SELECT * FROM Books;
```

#### Get Book by ID
```sql
SELECT * FROM Books WHERE book_id = ?;
```

#### Update Book Details
```sql
UPDATE Books
SET title = ?, author = ?, isbn = ?, quantity = ?
WHERE book_id = ?;
```

#### Delete Book
```sql
-- Check if book is currently issued
SELECT * FROM IssuedBooks WHERE book_id = ? AND return_date IS NULL;

-- Delete book if not issued
DELETE FROM Books WHERE book_id = ?;
```

#### Search Books
```sql
-- Search by title, author, or ISBN
SELECT * FROM Books
WHERE title LIKE ? OR author LIKE ? OR isbn LIKE ?;

-- Search specifically by ISBN
SELECT * FROM Books
WHERE isbn LIKE ?;
```

### 4. Student Management Operations

#### Add New Student
```sql
INSERT INTO Students (name, roll_no, department, email) VALUES (?, ?, ?, ?);
```

#### Get All Students
```sql
SELECT * FROM Students;
```

#### Get Student by ID
```sql
SELECT * FROM Students WHERE student_id = ?;
```

#### Update Student Details
```sql
UPDATE Students
SET name = ?, roll_no = ?, department = ?, email = ?
WHERE student_id = ?;
```

#### Delete Student
```sql
-- Check if student has issued books
SELECT * FROM IssuedBooks WHERE student_id = ? AND return_date IS NULL;

-- Delete student if no issued books
DELETE FROM Students WHERE student_id = ?;
```

#### Search Students
```sql
-- Search by ID or roll number
SELECT * FROM Students
WHERE student_id LIKE ? OR roll_no LIKE ?;

-- Search by email
SELECT * FROM Students
WHERE email LIKE ?;
```

### 5. Book Issue/Return Operations

#### Issue Book to Student
```sql
-- Check if student already has this book
SELECT * FROM IssuedBooks
WHERE book_id = ? AND student_id = ? AND return_date IS NULL;

-- Issue the book
INSERT INTO IssuedBooks (book_id, student_id, issue_date, due_date)
VALUES (?, ?, ?, ?);

-- Update book quantity
UPDATE Books SET quantity = quantity - 1 WHERE book_id = ?;
```

#### Return Book
```sql
-- Get issue record
SELECT * FROM IssuedBooks WHERE issue_id = ? AND return_date IS NULL;

-- Update return date
UPDATE IssuedBooks SET return_date = ? WHERE issue_id = ?;

-- Update book quantity
UPDATE Books SET quantity = quantity + 1 WHERE book_id = ?;
```

#### Get All Issued Books (with JOIN)
```sql
SELECT i.issue_id, i.issue_date, i.due_date, i.return_date,
       b.book_id, b.title, b.author, b.isbn,
       s.student_id, s.name, s.roll_no, s.department
FROM IssuedBooks i
JOIN Books b ON i.book_id = b.book_id
JOIN Students s ON i.student_id = s.student_id;
```

#### Get Books Issued to Specific Student
```sql
SELECT i.issue_id, i.issue_date, i.due_date, i.return_date,
       b.book_id, b.title, b.author, b.isbn
FROM IssuedBooks i
JOIN Books b ON i.book_id = b.book_id
WHERE i.student_id = ?;
```

## Sample Data Insertion

### Insert Sample Admin
```sql
INSERT INTO Admin (username, password) 
VALUES ('admin', 'hashed_password_here');
```

### Insert Sample Books
```sql
INSERT INTO Books (title, author, isbn, quantity) VALUES
('The Great Gatsby', 'F. Scott Fitzgerald', '978-0-7432-7356-5', 5),
('To Kill a Mockingbird', 'Harper Lee', '978-0-06-112008-4', 3),
('1984', 'George Orwell', '978-0-452-28423-4', 4),
('Pride and Prejudice', 'Jane Austen', '978-0-14-143951-8', 2);
```

### Insert Sample Students
```sql
INSERT INTO Students (name, roll_no, department, email) VALUES
('John Doe', 'CS001', 'Computer Science', '<EMAIL>'),
('Jane Smith', 'EE002', 'Electrical Engineering', '<EMAIL>'),
('Mike Johnson', 'ME003', 'Mechanical Engineering', '<EMAIL>');
```

### Insert Sample Book Issues
```sql
INSERT INTO IssuedBooks (book_id, student_id, issue_date, due_date) VALUES
(1, 1, '2024-01-15', '2024-01-29'),
(2, 2, '2024-01-16', '2024-01-30'),
(3, 1, '2024-01-17', '2024-01-31');
```

## Advanced Queries

### Books Currently Issued
```sql
SELECT b.title, b.author, s.name, s.roll_no, i.issue_date, i.due_date
FROM IssuedBooks i
JOIN Books b ON i.book_id = b.book_id
JOIN Students s ON i.student_id = s.student_id
WHERE i.return_date IS NULL;
```

### Overdue Books
```sql
SELECT b.title, s.name, s.roll_no, i.due_date,
       (julianday('now') - julianday(i.due_date)) as days_overdue
FROM IssuedBooks i
JOIN Books b ON i.book_id = b.book_id
JOIN Students s ON i.student_id = s.student_id
WHERE i.return_date IS NULL 
AND date('now') > i.due_date;
```

### Books Available for Issue
```sql
SELECT title, author, isbn, quantity
FROM Books
WHERE quantity > 0;
```

### Student Book History
```sql
SELECT b.title, i.issue_date, i.due_date, i.return_date,
       CASE 
           WHEN i.return_date IS NULL THEN 'Currently Issued'
           WHEN date(i.return_date) > date(i.due_date) THEN 'Returned Late'
           ELSE 'Returned On Time'
       END as status
FROM IssuedBooks i
JOIN Books b ON i.book_id = b.book_id
WHERE i.student_id = ?
ORDER BY i.issue_date DESC;
```

## Database Features Implemented

1. **Data Integrity**: Foreign key constraints ensure referential integrity
2. **Unique Constraints**: Prevent duplicate ISBNs and roll numbers
3. **Parameterized Queries**: Protection against SQL injection attacks
4. **Transaction Management**: Automatic commit/rollback for data consistency
5. **Connection Pooling**: Efficient database connection management
6. **Error Handling**: Comprehensive error handling for all database operations

## Security Measures

1. **Password Hashing**: Admin passwords stored using SHA-256 hashing
2. **SQL Injection Prevention**: All queries use parameterized statements
3. **Input Validation**: Data validation before database operations
4. **Connection Security**: Proper connection opening and closing

## Performance Considerations

1. **Indexing**: Primary keys and unique constraints create automatic indexes
2. **Query Optimization**: Efficient JOIN operations for related data
3. **Connection Management**: Proper connection lifecycle management
4. **Prepared Statements**: Reusable query execution plans

---

**Project Status**: Complete and Functional
**Database Engine**: SQLite 3
**Programming Language**: Python 3
**Total Tables**: 4 (Admin, Books, Students, IssuedBooks)
**Total Relationships**: 2 Foreign Key relationships
