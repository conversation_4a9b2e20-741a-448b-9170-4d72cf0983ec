# Library Management System - Database Report

## Database Schema

### Tables Created

#### 1. Admin Table
```sql
CREATE TABLE Admin (
    admin_id INTEGER PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL
);
```

#### 2. Books Table
```sql
CREATE TABLE Books (
    book_id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    isbn TEXT UNIQUE NOT NULL,
    quantity INTEGER NOT NULL
);
```

#### 3. Students Table
```sql
CREATE TABLE Students (
    student_id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    roll_no TEXT UNIQUE NOT NULL,
    department TEXT NOT NULL,
    email TEXT NOT NULL
);
```

#### 4. IssuedBooks Table
```sql
CREATE TABLE IssuedBooks (
    issue_id INTEGER PRIMARY KEY,
    book_id INTEGER NOT NULL,
    student_id INTEGER NOT NULL,
    issue_date TEXT NOT NULL,
    due_date TEXT NOT NULL,
    return_date TEXT,
    FOREIGN KEY (book_id) REFERENCES Books (book_id),
    FOREIGN KEY (student_id) REFERENCES Students (student_id)
);
```

## Essential Database Operations

### Admin Operations
```sql
-- Admin Login
SELECT * FROM Admin WHERE username = ? AND password = ?;

-- Create Default Admin
INSERT INTO Admin (username, password) VALUES ('admin', 'hashed_password');
```

### Book Operations
```sql
-- Add Book
INSERT INTO Books (title, author, isbn, quantity) VALUES (?, ?, ?, ?);

-- View All Books
SELECT * FROM Books;

-- Update Book
UPDATE Books SET title = ?, author = ?, isbn = ?, quantity = ? WHERE book_id = ?;

-- Delete Book
DELETE FROM Books WHERE book_id = ?;

-- Search Books
SELECT * FROM Books WHERE title LIKE ? OR author LIKE ? OR isbn LIKE ?;
```

### Student Operations
```sql
-- Add Student
INSERT INTO Students (name, roll_no, department, email) VALUES (?, ?, ?, ?);

-- View All Students
SELECT * FROM Students;

-- Update Student
UPDATE Students SET name = ?, roll_no = ?, department = ?, email = ? WHERE student_id = ?;

-- Delete Student
DELETE FROM Students WHERE student_id = ?;
```

### Book Issue/Return Operations
```sql
-- Issue Book
INSERT INTO IssuedBooks (book_id, student_id, issue_date, due_date) VALUES (?, ?, ?, ?);
UPDATE Books SET quantity = quantity - 1 WHERE book_id = ?;

-- Return Book
UPDATE IssuedBooks SET return_date = ? WHERE issue_id = ?;
UPDATE Books SET quantity = quantity + 1 WHERE book_id = ?;

-- View Issued Books
SELECT i.issue_id, b.title, s.name, i.issue_date, i.due_date
FROM IssuedBooks i
JOIN Books b ON i.book_id = b.book_id
JOIN Students s ON i.student_id = s.student_id;
```

## Sample Data

### Sample Books
```sql
INSERT INTO Books (title, author, isbn, quantity) VALUES
('Python Programming', 'John Smith', '978-1234567890', 5),
('Database Systems', 'Jane Doe', '978-0987654321', 3),
('Web Development', 'Mike Johnson', '978-1122334455', 4);
```

### Sample Students
```sql
INSERT INTO Students (name, roll_no, department, email) VALUES
('Alice Brown', 'CS001', 'Computer Science', '<EMAIL>'),
('Bob Wilson', 'EE002', 'Electrical Engineering', '<EMAIL>'),
('Carol Davis', 'ME003', 'Mechanical Engineering', '<EMAIL>');
```

## Key Features Implemented

✅ **Database Creation**: 4 tables with proper relationships
✅ **CRUD Operations**: Create, Read, Update, Delete for all entities
✅ **Foreign Keys**: Referential integrity between tables
✅ **Search Functionality**: Search books and students
✅ **Book Issue/Return**: Complete lending system
✅ **Data Validation**: Input validation and error handling
✅ **Security**: Password hashing and SQL injection prevention
