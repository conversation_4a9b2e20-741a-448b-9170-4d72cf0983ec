from models import Admin, Book, Student, IssuedBook

class AuthController:
    def __init__(self):
        self.admin = Admin()

    def login(self, username, password):
        """Handle admin login"""
        return self.admin.authenticate(username, password)

    def change_password(self, username, old_password, new_password):
        """Handle password change"""
        return self.admin.change_password(username, old_password, new_password)


class BookController:
    def __init__(self):
        self.book = Book()

    def add_book(self, title, author, isbn, quantity):
        """Add a new book"""
        # Validate input
        if not title or not author or not isbn:
            return False, "Title, author, and ISBN are required"

        try:
            quantity = int(quantity)
            if quantity < 0:
                return False, "Quantity must be a positive number"
        except ValueError:
            return False, "Quantity must be a number"

        return self.book.add_book(title, author, isbn, quantity)

    def get_all_books(self):
        """Get all books"""
        return self.book.get_all_books()

    def get_book_by_id(self, book_id):
        """Get a book by ID"""
        try:
            book_id = int(book_id)
        except ValueError:
            return False, "Book ID must be a number"

        return self.book.get_book_by_id(book_id)

    def update_book(self, book_id, title, author, isbn, quantity):
        """Update a book"""
        # Validate input
        if not title or not author or not isbn:
            return False, "Title, author, and ISBN are required"

        try:
            book_id = int(book_id)
            quantity = int(quantity)
            if quantity < 0:
                return False, "Quantity must be a positive number"
        except ValueError:
            return False, "Book ID and quantity must be numbers"

        return self.book.update_book(book_id, title, author, isbn, quantity)

    def delete_book(self, book_id):
        """Delete a book"""
        try:
            book_id = int(book_id)
        except ValueError:
            return False, "Book ID must be a number"

        return self.book.delete_book(book_id)

    def search_books(self, search_term):
        """Search for books"""
        if not search_term:
            return False, "Search term is required"

        return self.book.search_books(search_term)

    def search_books_by_isbn(self, isbn):
        """Search for books by ISBN"""
        if not isbn:
            return False, "ISBN is required"

        return self.book.search_books_by_isbn(isbn)


class StudentController:
    def __init__(self):
        self.student = Student()

    def add_student(self, name, roll_no, department, email):
        """Add a new student"""
        # Validate input
        if not name or not roll_no or not department or not email:
            return False, "All fields are required"

        if '@' not in email:
            return False, "Invalid email format"

        return self.student.add_student(name, roll_no, department, email)

    def get_all_students(self):
        """Get all students"""
        return self.student.get_all_students()

    def get_student_by_id(self, student_id):
        """Get a student by ID"""
        try:
            student_id = int(student_id)
        except ValueError:
            return False, "Student ID must be a number"

        return self.student.get_student_by_id(student_id)

    def search_students_by_id(self, student_id):
        """Search students by ID or roll number"""
        if not student_id:
            return False, "Student ID is required"

        return self.student.search_students_by_id(student_id)

    def search_students_by_email(self, email):
        """Search students by email"""
        if not email:
            return False, "Email is required"

        return self.student.search_students_by_email(email)

    def update_student(self, student_id, name, roll_no, department, email):
        """Update a student"""
        # Validate input
        if not name or not roll_no or not department or not email:
            return False, "All fields are required"

        if '@' not in email:
            return False, "Invalid email format"

        try:
            student_id = int(student_id)
        except ValueError:
            return False, "Student ID must be a number"

        return self.student.update_student(student_id, name, roll_no, department, email)

    def delete_student(self, student_id):
        """Delete a student"""
        try:
            student_id = int(student_id)
        except ValueError:
            return False, "Student ID must be a number"

        return self.student.delete_student(student_id)


class IssueController:
    def __init__(self):
        self.issued_book = IssuedBook()

    def issue_book(self, book_id, student_id, days=14):
        """Issue a book to a student"""
        try:
            book_id = int(book_id)
            student_id = int(student_id)
            days = int(days)

            if days <= 0:
                return False, "Days must be a positive number"
        except ValueError:
            return False, "Book ID, student ID, and days must be numbers"

        return self.issued_book.issue_book(book_id, student_id, days)

    def return_book(self, issue_id):
        """Return a book"""
        try:
            issue_id = int(issue_id)
        except ValueError:
            return False, "Issue ID must be a number"

        return self.issued_book.return_book(issue_id)

    def get_all_issued_books(self):
        """Get all issued books"""
        return self.issued_book.get_all_issued_books()

    def get_issued_books_by_student(self, student_id):
        """Get books issued to a student"""
        try:
            student_id = int(student_id)
        except ValueError:
            return False, "Student ID must be a number"

        return self.issued_book.get_issued_books_by_student(student_id)
