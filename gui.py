import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, font
import datetime
import os
import sys
from datetime import datetime, timedelta
from controllers import AuthController, BookController, StudentController, IssueController

# Define color scheme for the application
COLORS = {
    "primary": "#1976D2",       # Primary blue color
    "primary_dark": "#1565C0",  # Darker blue for hover states
    "secondary": "#FF8F00",     # Orange accent color
    "danger": "#E53935",        # Red for warnings/delete
    "success": "#43A047",       # Green for success
    "light_gray": "#F5F5F5",    # Light gray for backgrounds
    "medium_gray": "#E0E0E0",   # Medium gray for borders
    "dark_gray": "#757575",     # Dark gray for text
    "white": "#FFFFFF",         # White
    "black": "#212121",         # Almost black
    "overdue": "#FFEBEE",       # Light red for overdue items
    "highlight": "#E3F2FD"      # Light blue for highlighting
}

class LibraryApp(tk.Tk):
    def __init__(self):
        super().__init__()

        self.title("Library Management System")

        # Set window to full screen or maximize it
        self.state('zoomed')  # This maximizes the window on Windows

        # Alternative approach for different platforms
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        self.geometry(f"{screen_width}x{screen_height}")

        self.resizable(True, True)
        self.configure(bg=COLORS["light_gray"])

        # Set application icon
        try:
            self.iconbitmap("lib.ico")
        except Exception as e:
            print(f"Icon error: {e}")  # Print error for debugging but continue without icon

        # Configure custom styles for the application
        self.configure_styles()

        # Initialize controllers
        self.auth_controller = AuthController()
        self.book_controller = BookController()
        self.student_controller = StudentController()
        self.issue_controller = IssueController()

        # Initialize variables
        self.current_user = None

        # Create a container frame
        self.container = tk.Frame(self, bg=COLORS["light_gray"])
        self.container.pack(side="top", fill="both", expand=True)
        self.container.grid_rowconfigure(0, weight=1)
        self.container.grid_columnconfigure(0, weight=1)

        # Dictionary to hold the frames
        self.frames = {}

        # Show login frame
        self.show_frame(LoginFrame)

    def configure_styles(self):
        """Configure custom styles for the application"""
        # Create custom fonts
        default_font = font.nametofont("TkDefaultFont")
        default_font.configure(family="Segoe UI", size=10)

        heading_font = font.Font(family="Segoe UI", size=12, weight="bold")
        button_font = font.Font(family="Segoe UI", size=10)
        title_font = font.Font(family="Segoe UI", size=16, weight="bold")

        # Configure ttk styles
        style = ttk.Style()

        # Configure Treeview
        style.configure("Treeview",
                        background=COLORS["white"],
                        foreground=COLORS["black"],
                        rowheight=25,
                        fieldbackground=COLORS["white"])

        style.configure("Treeview.Heading",
                        font=heading_font,
                        background=COLORS["primary"],
                        foreground=COLORS["white"])

        style.map("Treeview",
                 background=[('selected', COLORS["primary"])],
                 foreground=[('selected', COLORS["white"])])

        # Configure Notebook (tabs)
        style.configure("TNotebook",
                        background=COLORS["light_gray"],
                        tabmargins=[2, 5, 2, 0])

        style.configure("TNotebook.Tab",
                        background=COLORS["medium_gray"],
                        foreground=COLORS["black"],
                        padding=[10, 5],
                        font=button_font)

        style.map("TNotebook.Tab",
                 background=[("selected", COLORS["primary"])],
                 foreground=[("selected", COLORS["white"])],
                 expand=[("selected", [1, 1, 1, 0])])

        # Configure Buttons
        style.configure("TButton",
                        font=button_font,
                        background=COLORS["primary"],
                        foreground=COLORS["white"],
                        padding=5)

        style.map("TButton",
                 background=[("active", COLORS["primary_dark"])],
                 relief=[("pressed", "sunken")])

        # Configure other widgets
        style.configure("TLabel", font=default_font, background=COLORS["light_gray"])
        style.configure("TFrame", background=COLORS["light_gray"])
        style.configure("TEntry", fieldbackground=COLORS["white"])

        # Custom styles for specific buttons
        style.configure("Danger.TButton", background=COLORS["danger"])
        style.map("Danger.TButton", background=[("active", "#C62828")])  # Darker red

        style.configure("Success.TButton", background=COLORS["success"])
        style.map("Success.TButton", background=[("active", "#2E7D32")])  # Darker green

        style.configure("Secondary.TButton", background=COLORS["secondary"])
        style.map("Secondary.TButton", background=[("active", "#F57C00")])  # Darker orange

    def show_frame(self, frame_class, *args, **kwargs):
        """Show a frame for the given class"""
        # Create a new frame if it doesn't exist
        if frame_class not in self.frames:
            frame = frame_class(self.container, self, *args, **kwargs)
            self.frames[frame_class] = frame
            frame.grid(row=0, column=0, sticky="nsew")
        else:
            # If frame exists, refresh it
            frame = self.frames[frame_class]
            frame.refresh(*args, **kwargs)

        # Raise the frame to the top
        frame.tkraise()

    def logout(self):
        """Handle logout"""
        self.current_user = None
        # Clear all frames except login
        for frame_class in list(self.frames.keys()):
            if frame_class != LoginFrame:
                if self.frames[frame_class]:
                    self.frames[frame_class].destroy()
                del self.frames[frame_class]

        # Show login frame
        self.show_frame(LoginFrame)

    def show_message(self, title, message, is_error=False):
        """Show a message dialog"""
        if is_error:
            messagebox.showerror(title, message)
        else:
            messagebox.showinfo(title, message)


class BaseFrame(tk.Frame):
    def __init__(self, parent, controller):
        super().__init__(parent, bg=COLORS["light_gray"])
        self.controller = controller

    def refresh(self, *args, **kwargs):
        """Refresh the frame content"""
        pass

    def create_header(self, title):
        """Create a header with the given title"""
        header_frame = tk.Frame(self, bg=COLORS["primary"], height=70)
        header_frame.pack(side="top", fill="x")

        # Add a logo or icon (placeholder)
        try:
            # Try to load an icon if available
            logo_img = tk.PhotoImage(file="library_logo.png")
            logo_img = logo_img.subsample(10, 10)  # Resize the image
            logo_label = tk.Label(header_frame, image=logo_img, bg=COLORS["primary"])
            logo_label.image = logo_img  # Keep a reference
            logo_label.pack(side="left", padx=15, pady=10)
        except:
            # If no icon is available, use a text-based logo
            logo_label = tk.Label(
                header_frame,
                text="🏛️",  # Library building emoji as logo
                font=("Segoe UI", 24),
                bg=COLORS["primary"],
                fg=COLORS["white"]
            )
            logo_label.pack(side="left", padx=15, pady=10)

        title_label = tk.Label(
            header_frame,
            text=title,
            font=("Segoe UI", 18, "bold"),
            bg=COLORS["primary"],
            fg=COLORS["white"]
        )
        title_label.pack(side="left", padx=10, pady=15)

        if self.controller.current_user:
            # Create a frame for user info with a subtle background
            user_frame = tk.Frame(header_frame, bg=COLORS["primary_dark"], padx=10, pady=5, relief="flat", bd=0)
            user_frame.pack(side="right", padx=20, pady=10)

            # User icon
            user_icon = tk.Label(
                user_frame,
                text="👤",  # User emoji
                font=("Segoe UI", 12),
                bg=COLORS["primary_dark"],
                fg=COLORS["white"]
            )
            user_icon.pack(side="left", padx=5)

            # Username
            user_label = tk.Label(
                user_frame,
                text=f"{self.controller.current_user}",
                font=("Segoe UI", 11),
                bg=COLORS["primary_dark"],
                fg=COLORS["white"]
            )
            user_label.pack(side="left", padx=5)

    def create_button(self, parent, text, command, bg=None, fg=None, width=18):
        """Create a styled button using ttk for better appearance"""
        style_name = "TButton"

        # Use custom style based on the background color
        if bg == COLORS["danger"] or bg == "#e74c3c":
            style_name = "Danger.TButton"
        elif bg == COLORS["success"] or bg == "#27ae60":
            style_name = "Success.TButton"
        elif bg == COLORS["secondary"] or bg == "#f39c12"  or bg == "#FF8F00":
            style_name = "Secondary.TButton"
        elif bg == "#95a5a6":  # Gray button
            style_name = "TButton"  # Use default style

        # Create the button with increased width for better text visibility
        button = ttk.Button(
            parent,
            text=text,
            command=command,
            style=style_name,
            width=width
        )

        return button


class LoginFrame(BaseFrame):
    def __init__(self, parent, controller):
        super().__init__(parent, controller)

        # Create a modern login screen with a split design
        self.configure(bg=COLORS["white"])

        # Create a container for the login screen
        container = tk.Frame(self, bg=COLORS["white"])
        container.place(relx=0.5, rely=0.5, anchor="center")

        # Create a card-like frame for the login form
        card_frame = tk.Frame(container, bg=COLORS["white"], padx=40, pady=40,
                             relief="flat", bd=1)
        card_frame.pack(padx=50, pady=50)

        # Add a shadow effect
        card_frame.config(highlightbackground=COLORS["medium_gray"], highlightthickness=1)

        # Logo and title
        logo_frame = tk.Frame(card_frame, bg=COLORS["white"])
        logo_frame.pack(pady=(0, 30))

        # Try to load a logo image
        try:
            logo_img = tk.PhotoImage(file="library_logo.png")
            logo_img = logo_img.subsample(5, 5)  # Resize the image
            logo_label = tk.Label(logo_frame, image=logo_img, bg=COLORS["white"])
            logo_label.image = logo_img  # Keep a reference
            logo_label.pack()
        except:
            # If no image is available, use a text-based logo
            logo_label = tk.Label(
                logo_frame,
                text="🏛️",  # Library building emoji as logo
                font=("Segoe UI", 48),
                bg=COLORS["white"],
                fg=COLORS["primary"]
            )
            logo_label.pack()

        # Title
        title_label = tk.Label(
            logo_frame,
            text="Library Management System",
            font=("Segoe UI", 20, "bold"),
            bg=COLORS["white"],
            fg=COLORS["black"]
        )
        title_label.pack(pady=(10, 5))

        # Subtitle
        subtitle_label = tk.Label(
            logo_frame,
            text="Login to your account",
            font=("Segoe UI", 12),
            bg=COLORS["white"],
            fg=COLORS["dark_gray"]
        )
        subtitle_label.pack()

        # Username
        username_frame = tk.Frame(card_frame, bg=COLORS["white"], pady=5)
        username_frame.pack(fill="x", pady=5)

        username_label = tk.Label(
            username_frame,
            text="Username",
            font=("Segoe UI", 11),
            bg=COLORS["white"],
            fg=COLORS["dark_gray"],
            anchor="w"
        )
        username_label.pack(anchor="w", pady=(0, 5))

        self.username_entry = ttk.Entry(
            username_frame,
            font=("Segoe UI", 12),
            width=30
        )
        self.username_entry.pack(fill="x", ipady=8)  # Add some padding inside the entry

        # Password
        password_frame = tk.Frame(card_frame, bg=COLORS["white"], pady=5)
        password_frame.pack(fill="x", pady=5)

        password_label = tk.Label(
            password_frame,
            text="Password",
            font=("Segoe UI", 11),
            bg=COLORS["white"],
            fg=COLORS["dark_gray"],
            anchor="w"
        )
        password_label.pack(anchor="w", pady=(0, 5))

        self.password_entry = ttk.Entry(
            password_frame,
            font=("Segoe UI", 12),
            width=30,
            show="•"  # Use a bullet character for password
        )
        self.password_entry.pack(fill="x", ipady=8)

        # Login button
        button_frame = tk.Frame(card_frame, bg=COLORS["white"], pady=15)
        button_frame.pack(fill="x")

        login_button = ttk.Button(
            button_frame,
            text="Login",
            command=self.login,
            style="Success.TButton",
            width=15
        )
        login_button.pack(fill="x", ipady=5)

        # Footer
        footer_frame = tk.Frame(card_frame, bg=COLORS["white"], pady=10)
        footer_frame.pack(fill="x")

        footer_label = tk.Label(
            footer_frame,
            text="© 2023 Library Management System",
            font=("Segoe UI", 8),
            bg=COLORS["white"],
            fg=COLORS["dark_gray"]
        )
        footer_label.pack()

        # Bind Enter key to login
        self.username_entry.bind("<Return>", lambda event: self.password_entry.focus())
        self.password_entry.bind("<Return>", lambda event: self.login())

        # Set focus to username entry
        self.username_entry.focus_set()

    def login(self):
        """Handle login"""
        username = self.username_entry.get()
        password = self.password_entry.get()

        if not username or not password:
            self.controller.show_message("Login Error", "Please enter both username and password", True)
            return

        if self.controller.auth_controller.login(username, password):
            self.controller.current_user = username
            self.username_entry.delete(0, tk.END)
            self.password_entry.delete(0, tk.END)
            self.controller.show_frame(DashboardFrame)
        else:
            self.controller.show_message("Login Failed", "Invalid username or password. Please try again.", True)


class DashboardFrame(BaseFrame):
    def __init__(self, parent, controller):
        super().__init__(parent, controller)

        self.create_header("Library Management System - Dashboard")

        # Create a modern dashboard with a sidebar and content area
        main_container = tk.Frame(self, bg=COLORS["light_gray"])
        main_container.pack(fill="both", expand=True)

        # Create sidebar with a modern look
        sidebar_width = 240
        sidebar_frame = tk.Frame(main_container, bg=COLORS["primary"], width=sidebar_width)
        sidebar_frame.pack(side="left", fill="y")
        sidebar_frame.pack_propagate(False)  # Keep the width fixed

        # Add logo to sidebar
        logo_frame = tk.Frame(sidebar_frame, bg=COLORS["primary"], pady=20)
        logo_frame.pack(fill="x")

        # Try to load a logo image
        try:
            logo_img = tk.PhotoImage(file="library_logo_small.png")
            logo_label = tk.Label(logo_frame, image=logo_img, bg=COLORS["primary"])
            logo_label.image = logo_img  # Keep a reference
            logo_label.pack()
        except:
            # If no image is available, use a text-based logo
            logo_label = tk.Label(
                logo_frame,
                text="🏛️",  # Library building emoji as logo
                font=("Segoe UI", 24),
                bg=COLORS["primary"],
                fg=COLORS["white"]
            )
            logo_label.pack()

        # Add navigation menu to sidebar
        menu_frame = tk.Frame(sidebar_frame, bg=COLORS["primary"], pady=10)
        menu_frame.pack(fill="x")

        # Menu title
        menu_title = tk.Label(
            menu_frame,
            text="MAIN MENU",
            font=("Segoe UI", 10),
            bg=COLORS["primary"],
            fg=COLORS["medium_gray"],
            anchor="w"
        )
        menu_title.pack(fill="x", padx=20, pady=(0, 10))

        # Menu items with icons
        menu_items = [
            ("🏛️ Book Management", lambda: self.controller.show_frame(BookManagementFrame)),
            ("👥 Student Management", lambda: self.controller.show_frame(StudentManagementFrame)),
            ("🔄 Issue/Return Books", lambda: self.controller.show_frame(IssueReturnFrame)),
            ("🔑 Change Password", lambda: self.controller.show_frame(ChangePasswordFrame)),
            ("🚪 Logout", self.controller.logout)
        ]

        # Create menu buttons with hover effect
        for text, command in menu_items:
            # Create a frame for each menu item for better styling
            item_frame = tk.Frame(menu_frame, bg=COLORS["primary"])
            item_frame.pack(fill="x", pady=2)

            # Create the button with text
            menu_button = tk.Label(
                item_frame,
                text=text,
                font=("Segoe UI", 11),
                bg=COLORS["primary"],
                fg=COLORS["white"],
                anchor="w",
                padx=20,
                pady=12,
                cursor="hand2"  # Hand cursor on hover
            )
            menu_button.pack(fill="x")

            # Bind click and hover events
            menu_button.bind("<Button-1>", lambda e, cmd=command: cmd())
            menu_button.bind("<Enter>", lambda e, btn=menu_button: btn.config(bg=COLORS["primary_dark"]))
            menu_button.bind("<Leave>", lambda e, btn=menu_button: btn.config(bg=COLORS["primary"]))

        # Add version info at the bottom of sidebar
        version_frame = tk.Frame(sidebar_frame, bg=COLORS["primary"])
        version_frame.pack(side="bottom", fill="x", pady=10)

        version_label = tk.Label(
            version_frame,
            text="Version 1.0.0",
            font=("Segoe UI", 8),
            bg=COLORS["primary"],
            fg=COLORS["medium_gray"]
        )
        version_label.pack(side="right", padx=10)

        # Create main content area
        content_container = tk.Frame(main_container, bg=COLORS["light_gray"])
        content_container.pack(side="right", fill="both", expand=True)

        # Welcome section
        welcome_frame = tk.Frame(content_container, bg=COLORS["light_gray"], padx=40, pady=30)
        welcome_frame.pack(fill="x")

        # Welcome header
        welcome_header = tk.Label(
            welcome_frame,
            text=f"Welcome to the Library Management System",
            font=("Segoe UI", 24, "bold"),
            bg=COLORS["light_gray"],
            fg=COLORS["black"]
        )
        welcome_header.pack(anchor="w")

        # Welcome subheader
        welcome_subheader = tk.Label(
            welcome_frame,
            text="Select an option from the menu to get started",
            font=("Segoe UI", 12),
            bg=COLORS["light_gray"],
            fg=COLORS["dark_gray"]
        )
        welcome_subheader.pack(anchor="w", pady=(5, 0))

        # Dashboard stats section
        stats_container = tk.Frame(content_container, bg=COLORS["light_gray"], padx=40)
        stats_container.pack(fill="x", pady=20)

        # Stats header
        stats_header = tk.Label(
            stats_container,
            text="Dashboard Overview",
            font=("Segoe UI", 16, "bold"),
            bg=COLORS["light_gray"],
            fg=COLORS["black"]
        )
        stats_header.pack(anchor="w", pady=(0, 20))

        # Stats cards container
        stats_frame = tk.Frame(stats_container, bg=COLORS["light_gray"])
        stats_frame.pack(fill="x")

        # Configure grid for stats cards
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(2, weight=1)

        # Get stats data
        success, books = self.controller.book_controller.get_all_books()
        book_count = len(books) if success and books else 0

        success, students = self.controller.student_controller.get_all_students()
        student_count = len(students) if success and students else 0

        success, issued_books = self.controller.issue_controller.get_all_issued_books()
        issued_count = 0
        overdue_count = 0

        if success and issued_books:
            today = datetime.now().date()
            for book in issued_books:
                if not book['return_date']:
                    issued_count += 1
                    # Check if book is overdue
                    due_date = datetime.strptime(book['due_date'], "%Y-%m-%d").date()
                    if today > due_date:
                        overdue_count += 1

        # Stats data
        stats = [
            ("🏛️", "Total Books", book_count, COLORS["primary"]),
            ("👥", "Total Students", student_count, COLORS["secondary"]),
            ("📖", "Books Issued", issued_count, COLORS["success"]),
            ("⏰", "Overdue Books", overdue_count, COLORS["danger"])
        ]

        # Create stat cards
        for i, (icon, label, value, color) in enumerate(stats):
            # Create a card-like frame for each stat
            col = i % 4
            row = i // 4

            stat_card = tk.Frame(stats_frame, bg=COLORS["white"], padx=20, pady=15, relief="flat")
            stat_card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

            # Add a colored top border
            top_border = tk.Frame(stat_card, bg=color, height=4)
            top_border.pack(fill="x", side="top")

            # Icon and label in a horizontal layout
            header_frame = tk.Frame(stat_card, bg=COLORS["white"])
            header_frame.pack(fill="x", pady=(15, 10))

            # Icon
            icon_label = tk.Label(
                header_frame,
                text=icon,
                font=("Segoe UI", 16),
                bg=COLORS["white"],
                fg=color
            )
            icon_label.pack(side="left")

            # Label
            label_text = tk.Label(
                header_frame,
                text=label,
                font=("Segoe UI", 12),
                bg=COLORS["white"],
                fg=COLORS["dark_gray"]
            )
            label_text.pack(side="left", padx=(10, 0))

            # Value
            value_label = tk.Label(
                stat_card,
                text=str(value),
                font=("Segoe UI", 28, "bold"),
                bg=COLORS["white"],
                fg=COLORS["black"]
            )
            value_label.pack(anchor="w", pady=(0, 15))

        # Quick actions section
        actions_frame = tk.Frame(content_container, bg=COLORS["light_gray"], padx=40, pady=20)
        actions_frame.pack(fill="x")

        # Actions header
        actions_header = tk.Label(
            actions_frame,
            text="Quick Actions",
            font=("Segoe UI", 16, "bold"),
            bg=COLORS["light_gray"],
            fg=COLORS["black"]
        )
        actions_header.pack(anchor="w", pady=(0, 20))

        # Action buttons container
        buttons_frame = tk.Frame(actions_frame, bg=COLORS["light_gray"])
        buttons_frame.pack(fill="x")

        # Action buttons
        actions = [
            ("Add New Book", lambda: self.controller.show_frame(BookManagementFrame), "Success.TButton"),
            ("Add New Student", lambda: self.controller.show_frame(StudentManagementFrame), "Secondary.TButton"),
            ("Issue Book", lambda: self.controller.show_frame(IssueReturnFrame), "TButton"),
            ("View Overdue Books", lambda: self.show_overdue_books(), "Danger.TButton")
        ]

        for i, (text, command, style) in enumerate(actions):
            action_button = ttk.Button(
                buttons_frame,
                text=text,
                command=command,
                style=style,
                width=20
            )
            action_button.pack(side="left", padx=(0 if i == 0 else 10, 0), pady=5)

    def show_overdue_books(self):
        """Navigate to the overdue books tab in the IssueReturnFrame"""
        # First navigate to the IssueReturnFrame
        self.controller.show_frame(IssueReturnFrame)

        # Get the frame after it's been shown
        issue_return_frame = self.controller.frames.get(IssueReturnFrame)
        if issue_return_frame and hasattr(issue_return_frame, 'notebook'):
            # Give the frame time to fully render before selecting the tab
            self.after(100, lambda: issue_return_frame.notebook.select(4))


class BookManagementFrame(BaseFrame):
    def __init__(self, parent, controller):
        super().__init__(parent, controller)

        self.create_header("Book Management")

        # Create main content
        content_frame = tk.Frame(self, bg="#f0f0f0")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Create top buttons frame
        buttons_frame = tk.Frame(content_frame, bg="#f0f0f0")
        buttons_frame.pack(fill="x", pady=10)

        # Add buttons with increased width and better styling
        add_button = self.create_button(buttons_frame, "➕ Add New Book", self.show_add_book, bg=COLORS["success"], width=20)
        add_button.pack(side="left", padx=5, pady=5, ipady=5)

        refresh_button = self.create_button(buttons_frame, "🔄 Refresh", self.refresh, width=15)
        refresh_button.pack(side="left", padx=5, pady=5, ipady=5)

        # Make back button more prominent
        back_button = self.create_button(
            buttons_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        # Create search frame
        search_frame = tk.Frame(content_frame, bg="#f0f0f0")
        search_frame.pack(fill="x", pady=10)

        # Create a frame for search options
        search_options_frame = tk.Frame(search_frame, bg="#f0f0f0")
        search_options_frame.pack(side="top", fill="x", pady=2)

        # Create radio buttons for search type
        self.book_search_type = tk.StringVar(master=self)
        self.book_search_type.set("title")  # Default search by title

        title_radio = tk.Radiobutton(search_options_frame, text="Search by Title/Author",
                                    variable=self.book_search_type, value="title", bg="#f0f0f0")
        title_radio.pack(side="left", padx=5)

        isbn_radio = tk.Radiobutton(search_options_frame, text="Search by ISBN",
                                   variable=self.book_search_type, value="isbn", bg="#f0f0f0")
        isbn_radio.pack(side="left", padx=5)

        # Create search input frame
        search_input_frame = tk.Frame(search_frame, bg="#f0f0f0")
        search_input_frame.pack(side="top", fill="x", pady=5)

        tk.Label(search_input_frame, text="Search:", bg="#f0f0f0").pack(side="left", padx=5)
        self.search_entry = tk.Entry(search_input_frame, width=30)
        self.search_entry.pack(side="left", padx=5)

        search_button = self.create_button(search_input_frame, "Search", self.search_books, width=10)
        search_button.pack(side="left", padx=5)

        # Create table frame
        table_frame = tk.Frame(content_frame, bg="white")
        table_frame.pack(fill="both", expand=True, pady=10)

        # Create treeview
        columns = ("ID", "Title", "Author", "ISBN", "Quantity")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings")

        # Define headings
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)

        # Adjust column widths
        self.tree.column("ID", width=50)
        self.tree.column("Title", width=200)
        self.tree.column("Author", width=150)
        self.tree.column("ISBN", width=120)
        self.tree.column("Quantity", width=80)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind double-click event
        self.tree.bind("<Double-1>", self.on_item_double_click)

        # Load books
        self.load_books()

    def refresh(self, *args, **kwargs):
        """Refresh the book list"""
        self.load_books()

    def load_books(self):
        """Load books into the treeview"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get all books
        success, books = self.controller.book_controller.get_all_books()

        if success and books:
            for book in books:
                self.tree.insert("", "end", values=(
                    book['book_id'],
                    book['title'],
                    book['author'],
                    book['isbn'],
                    book['quantity']
                ))

    def search_books(self):
        """Search books by title, author, or ISBN"""
        search_term = self.search_entry.get()
        search_type = self.book_search_type.get()  # Get the selected search type

        if not search_term:
            self.load_books()
            return

        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Search books based on search type
        if search_type == "isbn":
            # Search by ISBN only
            success, books = self.controller.book_controller.search_books_by_isbn(search_term)
            search_field = "ISBN"
        else:
            # Search by title or author (default)
            success, books = self.controller.book_controller.search_books(search_term)
            search_field = "title/author"

        if success and books:
            for book in books:
                self.tree.insert("", "end", values=(
                    book['book_id'],
                    book['title'],
                    book['author'],
                    book['isbn'],
                    book['quantity']
                ))
            self.controller.show_message("Search Results", f"Found {len(books)} books matching {search_field} '{search_term}'")
        elif success:
            self.controller.show_message("Search", f"No books found matching {search_field} '{search_term}'")

    def on_item_double_click(self, event):
        """Handle double-click on a book item"""
        # Get selected item
        selected_item = self.tree.selection()

        if not selected_item:
            return

        # Get book ID
        book_id = self.tree.item(selected_item[0], "values")[0]

        # Show book details dialog
        self.show_book_details(book_id)

    def show_book_details(self, book_id):
        """Show book details dialog"""
        success, book = self.controller.book_controller.get_book_by_id(book_id)

        if not success:
            self.controller.show_message("Error", book, True)
            return

        # Create dialog
        dialog = tk.Toplevel(self)
        dialog.title("Book Details")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()

        # Create form
        form_frame = tk.Frame(dialog, padx=20, pady=20)
        form_frame.pack(fill="both", expand=True)

        # Title
        tk.Label(form_frame, text="Title:").grid(row=0, column=0, sticky="w", pady=5)
        title_entry = tk.Entry(form_frame, width=30)
        title_entry.insert(0, book['title'])
        title_entry.grid(row=0, column=1, pady=5, padx=5)

        # Author
        tk.Label(form_frame, text="Author:").grid(row=1, column=0, sticky="w", pady=5)
        author_entry = tk.Entry(form_frame, width=30)
        author_entry.insert(0, book['author'])
        author_entry.grid(row=1, column=1, pady=5, padx=5)

        # ISBN
        tk.Label(form_frame, text="ISBN:").grid(row=2, column=0, sticky="w", pady=5)
        isbn_entry = tk.Entry(form_frame, width=30)
        isbn_entry.insert(0, book['isbn'])
        isbn_entry.grid(row=2, column=1, pady=5, padx=5)

        # Quantity
        tk.Label(form_frame, text="Quantity:").grid(row=3, column=0, sticky="w", pady=5)
        quantity_entry = tk.Entry(form_frame, width=30)
        quantity_entry.insert(0, book['quantity'])
        quantity_entry.grid(row=3, column=1, pady=5, padx=5)

        # Buttons frame
        buttons_frame = tk.Frame(form_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

        # Update button
        update_button = tk.Button(
            buttons_frame,
            text="Update",
            command=lambda: self.update_book(
                dialog,
                book_id,
                title_entry.get(),
                author_entry.get(),
                isbn_entry.get(),
                quantity_entry.get()
            ),
            bg="#3498db",
            fg="white",
            width=10
        )
        update_button.pack(side="left", padx=5)

        # Delete button
        delete_button = tk.Button(
            buttons_frame,
            text="Delete",
            command=lambda: self.delete_book(dialog, book_id),
            bg="#e74c3c",
            fg="white",
            width=10
        )
        delete_button.pack(side="left", padx=5)

        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            command=dialog.destroy,
            bg="#95a5a6",
            fg="white",
            width=10
        )
        cancel_button.pack(side="left", padx=5)

    def update_book(self, dialog, book_id, title, author, isbn, quantity):
        """Update a book"""
        success, message = self.controller.book_controller.update_book(book_id, title, author, isbn, quantity)

        if success:
            self.controller.show_message("Success", message)
            dialog.destroy()
            self.load_books()
        else:
            self.controller.show_message("Error", message, True)

    def delete_book(self, dialog, book_id):
        """Delete a book"""
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this book?"):
            success, message = self.controller.book_controller.delete_book(book_id)

            if success:
                self.controller.show_message("Success", message)
                dialog.destroy()
                self.load_books()
            else:
                self.controller.show_message("Error", message, True)

    def show_add_book(self):
        """Show add book dialog"""
        # Create dialog
        dialog = tk.Toplevel(self)
        dialog.title("Add New Book")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()

        # Create form
        form_frame = tk.Frame(dialog, padx=20, pady=20)
        form_frame.pack(fill="both", expand=True)

        # Title
        tk.Label(form_frame, text="Title:").grid(row=0, column=0, sticky="w", pady=5)
        title_entry = tk.Entry(form_frame, width=30)
        title_entry.grid(row=0, column=1, pady=5, padx=5)

        # Author
        tk.Label(form_frame, text="Author:").grid(row=1, column=0, sticky="w", pady=5)
        author_entry = tk.Entry(form_frame, width=30)
        author_entry.grid(row=1, column=1, pady=5, padx=5)

        # ISBN
        tk.Label(form_frame, text="ISBN:").grid(row=2, column=0, sticky="w", pady=5)
        isbn_entry = tk.Entry(form_frame, width=30)
        isbn_entry.grid(row=2, column=1, pady=5, padx=5)

        # Quantity
        tk.Label(form_frame, text="Quantity:").grid(row=3, column=0, sticky="w", pady=5)
        quantity_entry = tk.Entry(form_frame, width=30)
        quantity_entry.insert(0, "1")
        quantity_entry.grid(row=3, column=1, pady=5, padx=5)

        # Buttons frame
        buttons_frame = tk.Frame(form_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

        # Add button
        add_button = tk.Button(
            buttons_frame,
            text="Add Book",
            command=lambda: self.add_book(
                dialog,
                title_entry.get(),
                author_entry.get(),
                isbn_entry.get(),
                quantity_entry.get()
            ),
            bg="#3498db",
            fg="white",
            width=10
        )
        add_button.pack(side="left", padx=5)

        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            command=dialog.destroy,
            bg="#95a5a6",
            fg="white",
            width=10
        )
        cancel_button.pack(side="left", padx=5)

    def add_book(self, dialog, title, author, isbn, quantity):
        """Add a new book"""
        success, message = self.controller.book_controller.add_book(title, author, isbn, quantity)

        if success:
            self.controller.show_message("Success", message)
            dialog.destroy()
            self.load_books()
        else:
            self.controller.show_message("Error", message, True)


class StudentManagementFrame(BaseFrame):
    def __init__(self, parent, controller):
        super().__init__(parent, controller)

        self.create_header("Student Management")

        # Create main content
        content_frame = tk.Frame(self, bg="#f0f0f0")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Create top buttons frame
        buttons_frame = tk.Frame(content_frame, bg="#f0f0f0")
        buttons_frame.pack(fill="x", pady=10)

        # Add buttons with increased width and better styling
        add_button = self.create_button(buttons_frame, "➕ Add New Student", self.show_add_student, bg=COLORS["success"], width=20)
        add_button.pack(side="left", padx=5, pady=5, ipady=5)

        refresh_button = self.create_button(buttons_frame, "🔄 Refresh", self.refresh, width=15)
        refresh_button.pack(side="left", padx=5, pady=5, ipady=5)

        # Make back button more prominent
        back_button = self.create_button(
            buttons_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        # Create search frame
        search_frame = tk.Frame(content_frame, bg="#f0f0f0")
        search_frame.pack(fill="x", pady=10)

        # Create a frame for search options
        search_options_frame = tk.Frame(search_frame, bg="#f0f0f0")
        search_options_frame.pack(side="top", fill="x", pady=2)

        # Create radio buttons for search type
        self.student_search_type = tk.StringVar(master=self)
        self.student_search_type.set("name")  # Default search by name

        name_radio = tk.Radiobutton(search_options_frame, text="Search by Name/Department",
                                   variable=self.student_search_type, value="name", bg="#f0f0f0")
        name_radio.pack(side="left", padx=5)

        id_radio = tk.Radiobutton(search_options_frame, text="Search by ID/Roll No",
                                 variable=self.student_search_type, value="id", bg="#f0f0f0")
        id_radio.pack(side="left", padx=5)

        email_radio = tk.Radiobutton(search_options_frame, text="Search by Email",
                                    variable=self.student_search_type, value="email", bg="#f0f0f0")
        email_radio.pack(side="left", padx=5)

        # Create search input frame
        search_input_frame = tk.Frame(search_frame, bg="#f0f0f0")
        search_input_frame.pack(side="top", fill="x", pady=5)

        tk.Label(search_input_frame, text="Search:", bg="#f0f0f0").pack(side="left", padx=5)
        self.search_entry = tk.Entry(search_input_frame, width=30)
        self.search_entry.pack(side="left", padx=5)

        search_button = self.create_button(search_input_frame, "Search", self.search_students, width=10)
        search_button.pack(side="left", padx=5)

        # Create table frame
        table_frame = tk.Frame(content_frame, bg="white")
        table_frame.pack(fill="both", expand=True, pady=10)

        # Create treeview
        columns = ("ID", "Name", "Roll No", "Department", "Email")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings")

        # Define headings
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)

        # Adjust column widths
        self.tree.column("ID", width=50)
        self.tree.column("Name", width=150)
        self.tree.column("Roll No", width=100)
        self.tree.column("Department", width=150)
        self.tree.column("Email", width=200)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind double-click event
        self.tree.bind("<Double-1>", self.on_item_double_click)

        # Load students
        self.load_students()

    def refresh(self, *args, **kwargs):
        """Refresh the student list"""
        self.load_students()

    def load_students(self):
        """Load students into the treeview"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get all students
        success, students = self.controller.student_controller.get_all_students()

        if success and students:
            for student in students:
                self.tree.insert("", "end", values=(
                    student['student_id'],
                    student['name'],
                    student['roll_no'],
                    student['department'],
                    student['email']
                ))

    def search_students(self):
        """Search students by name, roll number, department, or email"""
        search_term = self.search_entry.get()
        search_type = self.student_search_type.get()  # Get the selected search type

        if not search_term:
            self.load_students()
            return

        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Search students based on search type
        if search_type == "id":
            # Search by ID or roll number
            success, students = self.controller.student_controller.search_students_by_id(search_term)
            search_field = "ID/Roll No"
        elif search_type == "email":
            # Search by email
            success, students = self.controller.student_controller.search_students_by_email(search_term)
            search_field = "email"
        else:
            # Search by name or department (default)
            # Get all students and filter
            success, all_students = self.controller.student_controller.get_all_students()
            students = []
            if success and all_students:
                for student in all_students:
                    if (search_term.lower() in student['name'].lower() or
                        search_term.lower() in student['department'].lower()):
                        students.append(student)
            search_field = "name/department"

        if success and students:
            for student in students:
                self.tree.insert("", "end", values=(
                    student['student_id'],
                    student['name'],
                    student['roll_no'],
                    student['department'],
                    student['email']
                ))
            self.controller.show_message("Search Results", f"Found {len(students)} students matching {search_field} '{search_term}'")
        elif success:
            self.controller.show_message("Search", f"No students found matching {search_field} '{search_term}'")
        else:
            self.controller.show_message("Error", "Failed to retrieve students")

    def on_item_double_click(self, event):
        """Handle double-click on a student item"""
        # Get selected item
        selected_item = self.tree.selection()

        if not selected_item:
            return

        # Get student ID
        student_id = self.tree.item(selected_item[0], "values")[0]

        # Show student details dialog
        self.show_student_details(student_id)

    def show_student_details(self, student_id):
        """Show student details dialog"""
        success, student = self.controller.student_controller.get_student_by_id(student_id)

        if not success:
            self.controller.show_message("Error", student, True)
            return

        # Create dialog
        dialog = tk.Toplevel(self)
        dialog.title("Student Details")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()

        # Create form
        form_frame = tk.Frame(dialog, padx=20, pady=20)
        form_frame.pack(fill="both", expand=True)

        # Name
        tk.Label(form_frame, text="Name:").grid(row=0, column=0, sticky="w", pady=5)
        name_entry = tk.Entry(form_frame, width=30)
        name_entry.insert(0, student['name'])
        name_entry.grid(row=0, column=1, pady=5, padx=5)

        # Roll No
        tk.Label(form_frame, text="Roll No:").grid(row=1, column=0, sticky="w", pady=5)
        roll_no_entry = tk.Entry(form_frame, width=30)
        roll_no_entry.insert(0, student['roll_no'])
        roll_no_entry.grid(row=1, column=1, pady=5, padx=5)

        # Department
        tk.Label(form_frame, text="Department:").grid(row=2, column=0, sticky="w", pady=5)
        department_entry = tk.Entry(form_frame, width=30)
        department_entry.insert(0, student['department'])
        department_entry.grid(row=2, column=1, pady=5, padx=5)

        # Email
        tk.Label(form_frame, text="Email:").grid(row=3, column=0, sticky="w", pady=5)
        email_entry = tk.Entry(form_frame, width=30)
        email_entry.insert(0, student['email'])
        email_entry.grid(row=3, column=1, pady=5, padx=5)

        # Buttons frame
        buttons_frame = tk.Frame(form_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

        # Update button
        update_button = tk.Button(
            buttons_frame,
            text="Update",
            command=lambda: self.update_student(
                dialog,
                student_id,
                name_entry.get(),
                roll_no_entry.get(),
                department_entry.get(),
                email_entry.get()
            ),
            bg="#3498db",
            fg="white",
            width=10
        )
        update_button.pack(side="left", padx=5)

        # Delete button
        delete_button = tk.Button(
            buttons_frame,
            text="Delete",
            command=lambda: self.delete_student(dialog, student_id),
            bg="#e74c3c",
            fg="white",
            width=10
        )
        delete_button.pack(side="left", padx=5)

        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            command=dialog.destroy,
            bg="#95a5a6",
            fg="white",
            width=10
        )
        cancel_button.pack(side="left", padx=5)

    def update_student(self, dialog, student_id, name, roll_no, department, email):
        """Update a student"""
        success, message = self.controller.student_controller.update_student(student_id, name, roll_no, department, email)

        if success:
            self.controller.show_message("Success", message)
            dialog.destroy()
            self.load_students()
        else:
            self.controller.show_message("Error", message, True)

    def delete_student(self, dialog, student_id):
        """Delete a student"""
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this student?"):
            success, message = self.controller.student_controller.delete_student(student_id)

            if success:
                self.controller.show_message("Success", message)
                dialog.destroy()
                self.load_students()
            else:
                self.controller.show_message("Error", message, True)

    def show_add_student(self):
        """Show add student dialog"""
        # Create dialog
        dialog = tk.Toplevel(self)
        dialog.title("Add New Student")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()

        # Create form
        form_frame = tk.Frame(dialog, padx=20, pady=20)
        form_frame.pack(fill="both", expand=True)

        # Name
        tk.Label(form_frame, text="Name:").grid(row=0, column=0, sticky="w", pady=5)
        name_entry = tk.Entry(form_frame, width=30)
        name_entry.grid(row=0, column=1, pady=5, padx=5)

        # Roll No
        tk.Label(form_frame, text="Roll No:").grid(row=1, column=0, sticky="w", pady=5)
        roll_no_entry = tk.Entry(form_frame, width=30)
        roll_no_entry.grid(row=1, column=1, pady=5, padx=5)

        # Department
        tk.Label(form_frame, text="Department:").grid(row=2, column=0, sticky="w", pady=5)
        department_entry = tk.Entry(form_frame, width=30)
        department_entry.grid(row=2, column=1, pady=5, padx=5)

        # Email
        tk.Label(form_frame, text="Email:").grid(row=3, column=0, sticky="w", pady=5)
        email_entry = tk.Entry(form_frame, width=30)
        email_entry.grid(row=3, column=1, pady=5, padx=5)

        # Buttons frame
        buttons_frame = tk.Frame(form_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

        # Add button
        add_button = tk.Button(
            buttons_frame,
            text="Add Student",
            command=lambda: self.add_student(
                dialog,
                name_entry.get(),
                roll_no_entry.get(),
                department_entry.get(),
                email_entry.get()
            ),
            bg="#3498db",
            fg="white",
            width=10
        )
        add_button.pack(side="left", padx=5)

        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            command=dialog.destroy,
            bg="#95a5a6",
            fg="white",
            width=10
        )
        cancel_button.pack(side="left", padx=5)

    def add_student(self, dialog, name, roll_no, department, email):
        """Add a new student"""
        success, message = self.controller.student_controller.add_student(name, roll_no, department, email)

        if success:
            self.controller.show_message("Success", message)
            dialog.destroy()
            self.load_students()
        else:
            self.controller.show_message("Error", message, True)


class IssueReturnFrame(BaseFrame):
    def __init__(self, parent, controller):
        super().__init__(parent, controller)

        self.create_header("Issue/Return Books")

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=20)

        # Create tabs
        self.issue_tab = tk.Frame(self.notebook, bg="#f0f0f0")
        self.return_tab = tk.Frame(self.notebook, bg="#f0f0f0")
        self.all_issued_tab = tk.Frame(self.notebook, bg="#f0f0f0")
        self.student_issued_tab = tk.Frame(self.notebook, bg="#f0f0f0")
        self.overdue_tab = tk.Frame(self.notebook, bg="#f0f0f0")

        self.notebook.add(self.issue_tab, text="Issue Book")
        self.notebook.add(self.return_tab, text="Return Book")
        self.notebook.add(self.all_issued_tab, text="All Issued Books")
        self.notebook.add(self.student_issued_tab, text="Books by Student")
        self.notebook.add(self.overdue_tab, text="Overdue Books")

        # Setup each tab
        self.setup_issue_tab()
        self.setup_return_tab()
        self.setup_all_issued_tab()
        self.setup_student_issued_tab()
        self.setup_overdue_tab()

        # Back button at the bottom with improved styling
        back_frame = tk.Frame(self, bg=COLORS["light_gray"])
        back_frame.pack(fill="x", padx=20, pady=15)

        # Make back button more prominent
        back_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

    def setup_overdue_tab(self):
        """Setup the overdue books tab"""
        # Create main frame
        main_frame = tk.Frame(self.overdue_tab, bg="#f0f0f0")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Label with warning color
        header_frame = tk.Frame(main_frame, bg="#f0f0f0")
        header_frame.pack(fill="x", pady=5)

        # Add back button at the bottom
        back_frame = tk.Frame(main_frame, bg="#f0f0f0")
        back_frame.pack(side="bottom", fill="x", pady=5)

        back_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        tk.Label(header_frame, text="Overdue Books", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(side="left", pady=5)

        # Add a label explaining fines
        fine_label = tk.Label(header_frame, text="Fine: $1 per day overdue", font=("Arial", 10), bg="#f0f0f0", fg="#e74c3c")
        fine_label.pack(side="right", pady=5, padx=10)

        # Create treeview
        table_frame = tk.Frame(main_frame, bg="white")
        table_frame.pack(fill="both", expand=True, pady=5)

        # Create a frame for the treeview and scrollbars
        overdue_tree_frame = tk.Frame(table_frame)
        overdue_tree_frame.pack(fill="both", expand=True)

        columns = ("Issue ID", "Book Title", "Student Name", "Student ID", "Issue Date", "Due Date", "Days Overdue", "Fine")
        self.overdue_tree = ttk.Treeview(overdue_tree_frame, columns=columns, show="headings")

        # Define headings with better styling
        for col in columns:
            self.overdue_tree.heading(col, text=col, anchor="center")

        # Configure column widths
        self.overdue_tree.column("Issue ID", width=70, minwidth=70)
        self.overdue_tree.column("Book Title", width=200, minwidth=150)
        self.overdue_tree.column("Student Name", width=150, minwidth=120)
        self.overdue_tree.column("Student ID", width=80, minwidth=80)
        self.overdue_tree.column("Issue Date", width=100, minwidth=90)
        self.overdue_tree.column("Due Date", width=100, minwidth=90)
        self.overdue_tree.column("Days Overdue", width=100, minwidth=90)
        self.overdue_tree.column("Fine", width=80, minwidth=70)

        # Add vertical scrollbar
        overdue_y_scrollbar = ttk.Scrollbar(overdue_tree_frame, orient="vertical", command=self.overdue_tree.yview)
        self.overdue_tree.configure(yscrollcommand=overdue_y_scrollbar.set)

        # Add horizontal scrollbar
        overdue_x_scrollbar = ttk.Scrollbar(overdue_tree_frame, orient="horizontal", command=self.overdue_tree.xview)
        self.overdue_tree.configure(xscrollcommand=overdue_x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.overdue_tree.grid(row=0, column=0, sticky="nsew")
        overdue_y_scrollbar.grid(row=0, column=1, sticky="ns")
        overdue_x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        overdue_tree_frame.grid_rowconfigure(0, weight=1)
        overdue_tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.overdue_tree.tag_configure('oddrow', background='#f0f0f0')
        self.overdue_tree.tag_configure('evenrow', background='#ffffff')
        self.overdue_tree.tag_configure('overdue', background='#ffebee')  # Light red background for overdue items

        # Add buttons frame
        buttons_frame = tk.Frame(main_frame, bg="#f0f0f0")
        buttons_frame.pack(fill="x", pady=10)

        # Refresh button with improved styling
        refresh_button = self.create_button(buttons_frame, "🔄 Refresh", self.load_overdue_books, width=15)
        refresh_button.pack(side="left", padx=5, pady=5, ipady=5)

        # Collect Fine button with improved styling
        collect_fine_button = self.create_button(buttons_frame, "💰 Collect Fine", self.collect_fine, bg=COLORS["secondary"], width=20)
        collect_fine_button.pack(side="left", padx=5, pady=5, ipady=5)

        # Send reminder button with improved styling
        reminder_button = self.create_button(buttons_frame, "📧 Send Reminder", self.send_overdue_reminder, bg=COLORS["danger"], width=20)
        reminder_button.pack(side="left", padx=5, pady=5, ipady=5)

        # Add fine records frame
        fine_records_frame = tk.LabelFrame(main_frame, text="Fine Collection Records", bg="#f0f0f0", padx=10, pady=10)
        fine_records_frame.pack(fill="x", pady=10)

        # Create treeview for fine records
        fine_records_tree_frame = tk.Frame(fine_records_frame)
        fine_records_tree_frame.pack(fill="both", expand=True, pady=5)

        columns = ("Date", "Student ID", "Student Name", "Book Title", "Amount", "Receipt No")
        self.fine_records_tree = ttk.Treeview(fine_records_tree_frame, columns=columns, show="headings", height=5)

        # Define headings with better styling
        for col in columns:
            self.fine_records_tree.heading(col, text=col, anchor="center")

        # Configure column widths
        self.fine_records_tree.column("Date", width=100, minwidth=100)
        self.fine_records_tree.column("Student ID", width=80, minwidth=80)
        self.fine_records_tree.column("Student Name", width=150, minwidth=120)
        self.fine_records_tree.column("Book Title", width=200, minwidth=150)
        self.fine_records_tree.column("Amount", width=80, minwidth=80)
        self.fine_records_tree.column("Receipt No", width=100, minwidth=100)

        # Add scrollbars
        fine_y_scrollbar = ttk.Scrollbar(fine_records_tree_frame, orient="vertical", command=self.fine_records_tree.yview)
        self.fine_records_tree.configure(yscrollcommand=fine_y_scrollbar.set)

        fine_x_scrollbar = ttk.Scrollbar(fine_records_tree_frame, orient="horizontal", command=self.fine_records_tree.xview)
        self.fine_records_tree.configure(xscrollcommand=fine_x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.fine_records_tree.grid(row=0, column=0, sticky="nsew")
        fine_y_scrollbar.grid(row=0, column=1, sticky="ns")
        fine_x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        fine_records_tree_frame.grid_rowconfigure(0, weight=1)
        fine_records_tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.fine_records_tree.tag_configure('oddrow', background='#f0f0f0')
        self.fine_records_tree.tag_configure('evenrow', background='#ffffff')

        # Load data
        self.load_overdue_books()

    def collect_fine(self):
        """Collect fine from student for overdue book"""
        selected_item = self.overdue_tree.selection()

        if not selected_item:
            self.controller.show_message("Error", "Please select an overdue book to collect fine", True)
            return

        # Get issue details
        issue_id = self.overdue_tree.item(selected_item[0], "values")[0]
        book_title = self.overdue_tree.item(selected_item[0], "values")[1]
        student_name = self.overdue_tree.item(selected_item[0], "values")[2]
        student_id = self.overdue_tree.item(selected_item[0], "values")[3]
        days_overdue = self.overdue_tree.item(selected_item[0], "values")[6]
        fine_amount = self.overdue_tree.item(selected_item[0], "values")[7]

        # Create a dialog for fine collection
        dialog = tk.Toplevel(self)
        dialog.title("Collect Fine")
        dialog.geometry("400x350")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()

        # Create form
        form_frame = tk.Frame(dialog, padx=20, pady=20)
        form_frame.pack(fill="both", expand=True)

        # Book and student details
        details_frame = tk.LabelFrame(form_frame, text="Details", padx=10, pady=10)
        details_frame.pack(fill="x", pady=10)

        tk.Label(details_frame, text=f"Book: {book_title}").pack(anchor="w")
        tk.Label(details_frame, text=f"Student: {student_name} (ID: {student_id})").pack(anchor="w")
        tk.Label(details_frame, text=f"Days Overdue: {days_overdue}").pack(anchor="w")
        tk.Label(details_frame, text=f"Fine Amount: {fine_amount}").pack(anchor="w")

        # Payment details
        payment_frame = tk.LabelFrame(form_frame, text="Payment Details", padx=10, pady=10)
        payment_frame.pack(fill="x", pady=10)

        # Receipt number
        tk.Label(payment_frame, text="Receipt Number:").grid(row=0, column=0, sticky="w", pady=5)
        receipt_entry = tk.Entry(payment_frame, width=20)
        receipt_entry.grid(row=0, column=1, pady=5, padx=5)

        # Payment method
        tk.Label(payment_frame, text="Payment Method:").grid(row=1, column=0, sticky="w", pady=5)
        payment_method_var = tk.StringVar()
        payment_method_var.set("Cash")
        payment_methods = ["Cash", "Card", "Bank Transfer", "Other"]
        payment_method_dropdown = ttk.Combobox(payment_frame, textvariable=payment_method_var, values=payment_methods, width=18)
        payment_method_dropdown.grid(row=1, column=1, pady=5, padx=5)

        # Notes
        tk.Label(payment_frame, text="Notes:").grid(row=2, column=0, sticky="w", pady=5)
        notes_entry = tk.Text(payment_frame, width=20, height=3)
        notes_entry.grid(row=2, column=1, pady=5, padx=5)

        # Buttons
        buttons_frame = tk.Frame(form_frame)
        buttons_frame.pack(pady=15)

        # Collect button
        collect_button = tk.Button(
            buttons_frame,
            text="Collect Fine",
            command=lambda: self.process_fine_collection(
                dialog,
                issue_id,
                student_id,
                student_name,
                book_title,
                fine_amount.replace('$', ''),
                receipt_entry.get(),
                payment_method_var.get(),
                notes_entry.get("1.0", tk.END).strip()
            ),
            bg="#f39c12",
            fg="white",
            width=12
        )
        collect_button.pack(side="left", padx=5)

        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            command=dialog.destroy,
            bg="#95a5a6",
            fg="white",
            width=10
        )
        cancel_button.pack(side="left", padx=5)

    def process_fine_collection(self, dialog, issue_id, student_id, student_name, book_title, fine_amount, receipt_no, payment_method, notes):
        """Process the fine collection and record it"""
        # Validate input
        if not receipt_no:
            self.controller.show_message("Error", "Please enter a receipt number", True)
            return

        # Get current date
        current_date = datetime.now().strftime("%Y-%m-%d")

        # Add to fine records
        count = len(self.fine_records_tree.get_children())
        tag = 'evenrow' if count % 2 == 0 else 'oddrow'

        self.fine_records_tree.insert("", "end", values=(
            current_date,
            student_id,
            student_name,
            book_title,
            f"${fine_amount}",
            receipt_no
        ), tags=(tag,))

        # Show success message
        self.controller.show_message("Success",
                                    f"Fine of ${fine_amount} collected from {student_name} for book '{book_title}'.\n" +
                                    f"Receipt Number: {receipt_no}\n" +
                                    f"Payment Method: {payment_method}")

        # Close the dialog
        dialog.destroy()

        # Note: In a real application, this would also update a database table for fine records

    def send_overdue_reminder(self):
        """Send reminder to students with overdue books (placeholder for future implementation)"""
        selected_item = self.overdue_tree.selection()

        if not selected_item:
            self.controller.show_message("Error", "Please select a book to send reminder", True)
            return

        # Get issue details
        issue_id = self.overdue_tree.item(selected_item[0], "values")[0]
        student_name = self.overdue_tree.item(selected_item[0], "values")[2]
        book_title = self.overdue_tree.item(selected_item[0], "values")[1]

        # This is a placeholder for actual email functionality
        self.controller.show_message("Reminder Sent",
                                    f"A reminder has been sent to {student_name} for the book '{book_title}'.\n\n" +
                                    "Note: This is a placeholder. Actual email functionality would need to be implemented.")

    def load_overdue_books(self):
        """Load overdue books into the overdue treeview"""
        # Clear existing items
        for item in self.overdue_tree.get_children():
            self.overdue_tree.delete(item)

        # Get all issued books
        success, issued_books = self.controller.issue_controller.get_all_issued_books()

        if success and issued_books:
            count = 0
            today = datetime.now().date()

            for book in issued_books:
                # Skip books that have been returned
                if book['return_date']:
                    continue

                # Convert due_date string to date object
                due_date = datetime.strptime(book['due_date'], "%Y-%m-%d").date()

                # Check if book is overdue
                if today > due_date:
                    # Calculate days overdue and fine
                    days_overdue = (today - due_date).days
                    fine = days_overdue * 1  # $1 per day

                    # Use alternating row colors
                    tag = 'evenrow' if count % 2 == 0 else 'oddrow'
                    count += 1

                    self.overdue_tree.insert("", "end", values=(
                        book['issue_id'],
                        book['title'],
                        book['name'],
                        book['student_id'],
                        book['issue_date'],
                        book['due_date'],
                        days_overdue,
                        f"${fine}"
                    ), tags=(tag, 'overdue'))

            if count == 0:
                # No overdue books - just display an empty table
                # Don't show a pop-up message
                pass

    def refresh(self, *args, **kwargs):
        """Refresh all tabs"""
        self.load_available_books()
        self.load_students()
        self.load_issued_books_for_return()
        self.load_all_issued_books()
        self.load_overdue_books()
        # Student issued tab is refreshed when a student is selected

    def setup_issue_tab(self):
        """Setup the issue book tab"""
        # Create main container frame
        main_container = tk.Frame(self.issue_tab, bg="#f0f0f0")
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # Create a frame for the content area (will use grid)
        content_area = tk.Frame(main_container, bg="#f0f0f0")
        content_area.pack(fill="both", expand=True)

        # Configure grid for better space distribution
        content_area.grid_columnconfigure(0, weight=1)  # Left column
        content_area.grid_columnconfigure(1, weight=1)  # Right column
        content_area.grid_rowconfigure(0, weight=1)     # Make row expandable

        # Create left and right frames using grid
        left_frame = tk.Frame(content_area, bg="#f0f0f0")
        left_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        right_frame = tk.Frame(content_area, bg="#f0f0f0")
        right_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)

        # Add back button at the top
        back_frame = tk.Frame(main_container, bg="#f0f0f0")
        back_frame.pack(side="bottom", fill="x", pady=5)

        back_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        # Left frame - Available Books
        tk.Label(left_frame, text="Available Books", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(pady=5)

        # Add book search
        book_search_frame = tk.Frame(left_frame, bg="#f0f0f0")
        book_search_frame.pack(fill="x", pady=5)

        # Create a frame for search options
        search_options_frame = tk.Frame(book_search_frame, bg="#f0f0f0")
        search_options_frame.pack(side="top", fill="x", pady=2)

        # Create radio buttons for search type - ensure proper master reference
        self.book_search_type = tk.StringVar(master=self)
        self.book_search_type.set("title")  # Default search by title

        title_radio = tk.Radiobutton(search_options_frame, text="Search by Title",
                                    variable=self.book_search_type, value="title", bg="#f0f0f0")
        title_radio.pack(side="left", padx=5)

        isbn_radio = tk.Radiobutton(search_options_frame, text="Search by ISBN",
                                   variable=self.book_search_type, value="isbn", bg="#f0f0f0")
        isbn_radio.pack(side="left", padx=5)

        # Create search entry and button
        search_input_frame = tk.Frame(book_search_frame, bg="#f0f0f0")
        search_input_frame.pack(side="top", fill="x", pady=2)

        tk.Label(search_input_frame, text="Search:", bg="#f0f0f0").pack(side="left", padx=5)
        self.book_search_entry = tk.Entry(search_input_frame, width=20)
        self.book_search_entry.pack(side="left", padx=5)

        book_search_button = self.create_button(search_input_frame, "Search", self.search_books_for_issue, width=8)
        book_search_button.pack(side="left", padx=5)

        # Create treeview for books
        book_frame = tk.Frame(left_frame, bg="white")
        book_frame.pack(fill="both", expand=True, pady=5)

        # Create a frame for the treeview and scrollbars
        tree_frame = tk.Frame(book_frame)
        tree_frame.pack(fill="both", expand=True)

        columns = ("ID", "Title", "Author", "ISBN", "Quantity")
        self.book_tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # Define headings with better styling
        for col in columns:
            self.book_tree.heading(col, text=col, anchor="center")

        # Configure column widths as percentages of the total width
        self.book_tree.column("ID", width=50, minwidth=50)
        self.book_tree.column("Title", width=200, minwidth=150)
        self.book_tree.column("Author", width=150, minwidth=120)
        self.book_tree.column("ISBN", width=120, minwidth=100)
        self.book_tree.column("Quantity", width=80, minwidth=60)

        # Add vertical scrollbar
        y_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.book_tree.yview)
        self.book_tree.configure(yscrollcommand=y_scrollbar.set)

        # Add horizontal scrollbar
        x_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.book_tree.xview)
        self.book_tree.configure(xscrollcommand=x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.book_tree.grid(row=0, column=0, sticky="nsew")
        y_scrollbar.grid(row=0, column=1, sticky="ns")
        x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.book_tree.tag_configure('oddrow', background='#f0f0f0')
        self.book_tree.tag_configure('evenrow', background='#ffffff')

        # Right frame - Students
        tk.Label(right_frame, text="Students", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(pady=5)

        # Add student search
        student_search_frame = tk.Frame(right_frame, bg="#f0f0f0")
        student_search_frame.pack(fill="x", pady=5)

        # Create a frame for search options
        student_search_options_frame = tk.Frame(student_search_frame, bg="#f0f0f0")
        student_search_options_frame.pack(side="top", fill="x", pady=2)

        # Create radio buttons for search type - ensure proper master reference
        self.student_search_type = tk.StringVar(master=self)
        self.student_search_type.set("id")  # Default search by ID

        id_radio = tk.Radiobutton(student_search_options_frame, text="Search by ID",
                                 variable=self.student_search_type, value="id", bg="#f0f0f0")
        id_radio.pack(side="left", padx=5)

        email_radio = tk.Radiobutton(student_search_options_frame, text="Search by Email",
                                    variable=self.student_search_type, value="email", bg="#f0f0f0")
        email_radio.pack(side="left", padx=5)

        # Create search entry and button
        student_search_input_frame = tk.Frame(student_search_frame, bg="#f0f0f0")
        student_search_input_frame.pack(side="top", fill="x", pady=2)

        tk.Label(student_search_input_frame, text="Search:", bg="#f0f0f0").pack(side="left", padx=5)
        self.student_search_entry = tk.Entry(student_search_input_frame, width=20)
        self.student_search_entry.pack(side="left", padx=5)

        student_search_button = self.create_button(student_search_input_frame, "Search", self.search_students_for_issue, width=8)
        student_search_button.pack(side="left", padx=5)

        # Create treeview for students
        student_frame = tk.Frame(right_frame, bg="white")
        student_frame.pack(fill="both", expand=True, pady=5)

        # Create a frame for the treeview and scrollbars
        student_tree_frame = tk.Frame(student_frame)
        student_tree_frame.pack(fill="both", expand=True)

        columns = ("ID", "Name", "Roll No", "Email")
        self.student_tree = ttk.Treeview(student_tree_frame, columns=columns, show="headings")

        # Define headings with better styling
        for col in columns:
            self.student_tree.heading(col, text=col, anchor="center")

        # Configure column widths as percentages of the total width
        self.student_tree.column("ID", width=50, minwidth=50)
        self.student_tree.column("Name", width=150, minwidth=120)
        self.student_tree.column("Roll No", width=100, minwidth=80)
        self.student_tree.column("Email", width=200, minwidth=150)

        # Add vertical scrollbar
        student_y_scrollbar = ttk.Scrollbar(student_tree_frame, orient="vertical", command=self.student_tree.yview)
        self.student_tree.configure(yscrollcommand=student_y_scrollbar.set)

        # Add horizontal scrollbar
        student_x_scrollbar = ttk.Scrollbar(student_tree_frame, orient="horizontal", command=self.student_tree.xview)
        self.student_tree.configure(xscrollcommand=student_x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.student_tree.grid(row=0, column=0, sticky="nsew")
        student_y_scrollbar.grid(row=0, column=1, sticky="ns")
        student_x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        student_tree_frame.grid_rowconfigure(0, weight=1)
        student_tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.student_tree.tag_configure('oddrow', background='#f0f0f0')
        self.student_tree.tag_configure('evenrow', background='#ffffff')

        # Issue form at the bottom
        form_frame = tk.Frame(self.issue_tab, bg="#f0f0f0")
        form_frame.pack(side="bottom", fill="x", padx=10, pady=10)

        # Selected book and student labels
        selected_frame = tk.Frame(form_frame, bg="#f0f0f0")
        selected_frame.pack(fill="x", pady=5)

        tk.Label(selected_frame, text="Selected Book:", bg="#f0f0f0").grid(row=0, column=0, sticky="w")
        self.selected_book_label = tk.Label(selected_frame, text="None", bg="#f0f0f0", fg="#e74c3c")
        self.selected_book_label.grid(row=0, column=1, sticky="w", padx=5)

        tk.Label(selected_frame, text="Selected Student:", bg="#f0f0f0").grid(row=1, column=0, sticky="w")
        self.selected_student_label = tk.Label(selected_frame, text="None", bg="#f0f0f0", fg="#e74c3c")
        self.selected_student_label.grid(row=1, column=1, sticky="w", padx=5)

        # Days input
        days_frame = tk.Frame(form_frame, bg="#f0f0f0")
        days_frame.pack(fill="x", pady=5)

        tk.Label(days_frame, text="Days to Issue:", bg="#f0f0f0").pack(side="left")
        self.days_entry = tk.Entry(days_frame, width=10)
        self.days_entry.insert(0, "14")
        self.days_entry.pack(side="left", padx=5)

        # Issue button with improved styling
        issue_button = self.create_button(form_frame, "🏛️ Issue Book", self.issue_book, bg=COLORS["success"], width=20)
        issue_button.pack(pady=10, ipady=5)

        # Bind selection events
        self.book_tree.bind("<<TreeviewSelect>>", self.on_book_select)
        self.student_tree.bind("<<TreeviewSelect>>", self.on_student_select)

        # Load data
        self.load_available_books()
        self.load_students()

    def setup_return_tab(self):
        """Setup the return book tab"""
        # Create main frame
        main_frame = tk.Frame(self.return_tab, bg="#f0f0f0")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Label
        tk.Label(main_frame, text="Currently Issued Books", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(pady=5)

        # Add back button at the top
        back_frame = tk.Frame(main_frame, bg="#f0f0f0")
        back_frame.pack(side="bottom", fill="x", pady=5)

        back_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        # Create treeview
        table_frame = tk.Frame(main_frame, bg="white")
        table_frame.pack(fill="both", expand=True, pady=5)

        # Create a frame for the treeview and scrollbars
        return_tree_frame = tk.Frame(table_frame)
        return_tree_frame.pack(fill="both", expand=True)

        columns = ("Issue ID", "Book Title", "Student Name", "Issue Date", "Due Date")
        self.return_tree = ttk.Treeview(return_tree_frame, columns=columns, show="headings")

        # Define headings with better styling
        for col in columns:
            self.return_tree.heading(col, text=col, anchor="center")

        # Configure column widths
        self.return_tree.column("Issue ID", width=70, minwidth=70)
        self.return_tree.column("Book Title", width=250, minwidth=200)
        self.return_tree.column("Student Name", width=200, minwidth=150)
        self.return_tree.column("Issue Date", width=100, minwidth=100)
        self.return_tree.column("Due Date", width=100, minwidth=100)

        # Add vertical scrollbar
        return_y_scrollbar = ttk.Scrollbar(return_tree_frame, orient="vertical", command=self.return_tree.yview)
        self.return_tree.configure(yscrollcommand=return_y_scrollbar.set)

        # Add horizontal scrollbar
        return_x_scrollbar = ttk.Scrollbar(return_tree_frame, orient="horizontal", command=self.return_tree.xview)
        self.return_tree.configure(xscrollcommand=return_x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.return_tree.grid(row=0, column=0, sticky="nsew")
        return_y_scrollbar.grid(row=0, column=1, sticky="ns")
        return_x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        return_tree_frame.grid_rowconfigure(0, weight=1)
        return_tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.return_tree.tag_configure('oddrow', background='#f0f0f0')
        self.return_tree.tag_configure('evenrow', background='#ffffff')

        # Return form at the bottom
        form_frame = tk.Frame(self.return_tab, bg="#f0f0f0")
        form_frame.pack(side="bottom", fill="x", padx=10, pady=10)

        # Selected issue label
        selected_frame = tk.Frame(form_frame, bg="#f0f0f0")
        selected_frame.pack(fill="x", pady=5)

        tk.Label(selected_frame, text="Selected Issue:", bg="#f0f0f0").pack(side="left")
        self.selected_issue_label = tk.Label(selected_frame, text="None", bg="#f0f0f0", fg="#e74c3c")
        self.selected_issue_label.pack(side="left", padx=5)

        # Return button with improved styling
        return_button = self.create_button(form_frame, "📙 Return Book", self.return_book, bg=COLORS["secondary"], width=20)
        return_button.pack(pady=10, ipady=5)

        # Bind selection event
        self.return_tree.bind("<<TreeviewSelect>>", self.on_issue_select)

        # Load data
        self.load_issued_books_for_return()

    def setup_all_issued_tab(self):
        """Setup the all issued books tab"""
        # Create main frame
        main_frame = tk.Frame(self.all_issued_tab, bg="#f0f0f0")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Label
        tk.Label(main_frame, text="All Issued Books", font=("Arial", 12, "bold"), bg="#f0f0f0").pack(pady=5)

        # Add back button at the bottom
        back_frame = tk.Frame(main_frame, bg="#f0f0f0")
        back_frame.pack(side="bottom", fill="x", pady=5)

        back_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        # Create treeview
        table_frame = tk.Frame(main_frame, bg="white")
        table_frame.pack(fill="both", expand=True, pady=5)

        # Create a frame for the treeview and scrollbars
        all_issued_tree_frame = tk.Frame(table_frame)
        all_issued_tree_frame.pack(fill="both", expand=True)

        columns = ("Issue ID", "Book Title", "Student Name", "Issue Date", "Due Date", "Return Date", "Status")
        self.all_issued_tree = ttk.Treeview(all_issued_tree_frame, columns=columns, show="headings")

        # Define headings with better styling
        for col in columns:
            self.all_issued_tree.heading(col, text=col, anchor="center")

        # Configure column widths
        self.all_issued_tree.column("Issue ID", width=70, minwidth=70)
        self.all_issued_tree.column("Book Title", width=200, minwidth=150)
        self.all_issued_tree.column("Student Name", width=150, minwidth=120)
        self.all_issued_tree.column("Issue Date", width=100, minwidth=90)
        self.all_issued_tree.column("Due Date", width=100, minwidth=90)
        self.all_issued_tree.column("Return Date", width=100, minwidth=90)
        self.all_issued_tree.column("Status", width=80, minwidth=70)

        # Add vertical scrollbar
        all_issued_y_scrollbar = ttk.Scrollbar(all_issued_tree_frame, orient="vertical", command=self.all_issued_tree.yview)
        self.all_issued_tree.configure(yscrollcommand=all_issued_y_scrollbar.set)

        # Add horizontal scrollbar
        all_issued_x_scrollbar = ttk.Scrollbar(all_issued_tree_frame, orient="horizontal", command=self.all_issued_tree.xview)
        self.all_issued_tree.configure(xscrollcommand=all_issued_x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.all_issued_tree.grid(row=0, column=0, sticky="nsew")
        all_issued_y_scrollbar.grid(row=0, column=1, sticky="ns")
        all_issued_x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        all_issued_tree_frame.grid_rowconfigure(0, weight=1)
        all_issued_tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.all_issued_tree.tag_configure('oddrow', background='#f0f0f0')
        self.all_issued_tree.tag_configure('evenrow', background='#ffffff')

        # Refresh button
        refresh_button = self.create_button(main_frame, "Refresh", self.load_all_issued_books)
        refresh_button.pack(pady=10)

        # Load data
        self.load_all_issued_books()

    def setup_student_issued_tab(self):
        """Setup the books issued to student tab"""
        # Create top and bottom frames
        top_frame = tk.Frame(self.student_issued_tab, bg="#f0f0f0")
        top_frame.pack(fill="x", padx=10, pady=10)

        bottom_frame = tk.Frame(self.student_issued_tab, bg="#f0f0f0")
        bottom_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Add back button at the bottom
        back_frame = tk.Frame(bottom_frame, bg="#f0f0f0")
        back_frame.pack(side="bottom", fill="x", pady=5)

        back_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        back_button.pack(side="right", padx=10, pady=5, ipady=5)

        # Student selection
        tk.Label(top_frame, text="Select Student:", bg="#f0f0f0").pack(side="left")

        # Get all students
        success, students = self.controller.student_controller.get_all_students()

        student_names = []
        self.student_ids = {}

        if success and students:
            for student in students:
                name_with_id = f"{student['name']} ({student['roll_no']})"
                student_names.append(name_with_id)
                self.student_ids[name_with_id] = student['student_id']

        # Create dropdown with proper master reference
        self.student_var = tk.StringVar(master=self)
        if student_names:
            self.student_var.set(student_names[0])

        self.student_dropdown = ttk.Combobox(top_frame, textvariable=self.student_var, values=student_names, width=30)
        self.student_dropdown.pack(side="left", padx=5)

        # View button
        view_button = self.create_button(top_frame, "View Books", self.load_student_issued_books, width=10)
        view_button.pack(side="left", padx=5)

        # Create treeview
        table_frame = tk.Frame(bottom_frame, bg="white")
        table_frame.pack(fill="both", expand=True, pady=5)

        # Create a frame for the treeview and scrollbars
        student_issued_tree_frame = tk.Frame(table_frame)
        student_issued_tree_frame.pack(fill="both", expand=True)

        columns = ("Issue ID", "Book Title", "Author", "Issue Date", "Due Date", "Return Date", "Status")
        self.student_issued_tree = ttk.Treeview(student_issued_tree_frame, columns=columns, show="headings")

        # Define headings with better styling
        for col in columns:
            self.student_issued_tree.heading(col, text=col, anchor="center")

        # Configure column widths
        self.student_issued_tree.column("Issue ID", width=70, minwidth=70)
        self.student_issued_tree.column("Book Title", width=200, minwidth=150)
        self.student_issued_tree.column("Author", width=150, minwidth=120)
        self.student_issued_tree.column("Issue Date", width=100, minwidth=90)
        self.student_issued_tree.column("Due Date", width=100, minwidth=90)
        self.student_issued_tree.column("Return Date", width=100, minwidth=90)
        self.student_issued_tree.column("Status", width=80, minwidth=70)

        # Add vertical scrollbar
        student_issued_y_scrollbar = ttk.Scrollbar(student_issued_tree_frame, orient="vertical", command=self.student_issued_tree.yview)
        self.student_issued_tree.configure(yscrollcommand=student_issued_y_scrollbar.set)

        # Add horizontal scrollbar
        student_issued_x_scrollbar = ttk.Scrollbar(student_issued_tree_frame, orient="horizontal", command=self.student_issued_tree.xview)
        self.student_issued_tree.configure(xscrollcommand=student_issued_x_scrollbar.set)

        # Grid layout for treeview and scrollbars
        self.student_issued_tree.grid(row=0, column=0, sticky="nsew")
        student_issued_y_scrollbar.grid(row=0, column=1, sticky="ns")
        student_issued_x_scrollbar.grid(row=1, column=0, sticky="ew")

        # Configure grid weights
        student_issued_tree_frame.grid_rowconfigure(0, weight=1)
        student_issued_tree_frame.grid_columnconfigure(0, weight=1)

        # Apply alternating row colors for better readability
        self.student_issued_tree.tag_configure('oddrow', background='#f0f0f0')
        self.student_issued_tree.tag_configure('evenrow', background='#ffffff')

        # Load data for first student if available
        if student_names:
            self.load_student_issued_books()

    def load_available_books(self):
        """Load available books into the treeview"""
        # Clear existing items
        for item in self.book_tree.get_children():
            self.book_tree.delete(item)

        # Get all books
        success, books = self.controller.book_controller.get_all_books()

        if success and books:
            count = 0
            for book in books:
                if book['quantity'] > 0:  # Only show books with available copies
                    # Use alternating row colors
                    tag = 'evenrow' if count % 2 == 0 else 'oddrow'
                    count += 1

                    self.book_tree.insert("", "end", values=(
                        book['book_id'],
                        book['title'],
                        book['author'],
                        book['isbn'],
                        book['quantity']
                    ), tags=(tag,))

    def search_books_for_issue(self):
        """Search books by title or ISBN for issuing"""
        search_term = self.book_search_entry.get()
        search_type = self.book_search_type.get()  # Get the selected search type (title or isbn)

        if not search_term:
            self.load_available_books()
            return

        # Clear existing items
        for item in self.book_tree.get_children():
            self.book_tree.delete(item)

        # Get all books
        success, books = self.controller.book_controller.get_all_books()

        if success and books:
            found_books = []
            for book in books:
                # Only show books with available copies
                if book['quantity'] <= 0:
                    continue

                # Check if search term matches based on search type
                if search_type == "isbn" and search_term.lower() in book['isbn'].lower():
                    self.book_tree.insert("", "end", values=(
                        book['book_id'],
                        book['title'],
                        book['author'],
                        book['isbn'],
                        book['quantity']
                    ))
                    found_books.append(book)
                elif search_type == "title" and search_term.lower() in book['title'].lower():
                    self.book_tree.insert("", "end", values=(
                        book['book_id'],
                        book['title'],
                        book['author'],
                        book['isbn'],
                        book['quantity']
                    ))
                    found_books.append(book)

            if found_books:
                search_field = "title" if search_type == "title" else "ISBN"
                self.controller.show_message("Search Results", f"Found {len(found_books)} books matching {search_field} '{search_term}'")
            else:
                search_field = "title" if search_type == "title" else "ISBN"
                self.controller.show_message("Search", f"No available books found matching this {search_field}")
        else:
            self.controller.show_message("Error", "Failed to retrieve books")

    def load_students(self):
        """Load students into the treeview"""
        # Clear existing items
        for item in self.student_tree.get_children():
            self.student_tree.delete(item)

        # Get all students
        success, students = self.controller.student_controller.get_all_students()

        if success and students:
            count = 0
            for student in students:
                # Use alternating row colors
                tag = 'evenrow' if count % 2 == 0 else 'oddrow'
                count += 1

                self.student_tree.insert("", "end", values=(
                    student['student_id'],
                    student['name'],
                    student['roll_no'],
                    student['email']
                ), tags=(tag,))

    def search_students_for_issue(self):
        """Search students by ID or email for issuing"""
        search_term = self.student_search_entry.get()
        search_type = self.student_search_type.get()  # Get the selected search type (id or email)

        if not search_term:
            self.load_students()
            return

        # Clear existing items
        for item in self.student_tree.get_children():
            self.student_tree.delete(item)

        # Get all students
        success, students = self.controller.student_controller.get_all_students()

        if success and students:
            found_students = []
            for student in students:
                # Check if search term matches based on search type
                if search_type == "id":
                    # Try to match student ID or roll number
                    student_id_str = str(student['student_id'])
                    if (search_term.lower() in student_id_str.lower() or
                        search_term.lower() in student['roll_no'].lower()):
                        self.student_tree.insert("", "end", values=(
                            student['student_id'],
                            student['name'],
                            student['roll_no'],
                            student['email']
                        ))
                        found_students.append(student)
                elif search_type == "email" and search_term.lower() in student['email'].lower():
                    self.student_tree.insert("", "end", values=(
                        student['student_id'],
                        student['name'],
                        student['roll_no'],
                        student['email']
                    ))
                    found_students.append(student)

            if found_students:
                search_field = "ID/Roll No" if search_type == "id" else "email"
                self.controller.show_message("Search Results", f"Found {len(found_students)} students matching {search_field} '{search_term}'")
            else:
                search_field = "ID/Roll No" if search_type == "id" else "email"
                self.controller.show_message("Search", f"No students found matching this {search_field}")
        else:
            self.controller.show_message("Error", "Failed to retrieve students")

    def load_issued_books_for_return(self):
        """Load issued books into the return treeview"""
        # Clear existing items
        for item in self.return_tree.get_children():
            self.return_tree.delete(item)

        # Get all issued books
        success, issued_books = self.controller.issue_controller.get_all_issued_books()

        if success and issued_books:
            count = 0
            for book in issued_books:
                if not book['return_date']:  # Only show books that haven't been returned
                    # Use alternating row colors
                    tag = 'evenrow' if count % 2 == 0 else 'oddrow'
                    count += 1

                    self.return_tree.insert("", "end", values=(
                        book['issue_id'],
                        book['title'],
                        book['name'],
                        book['issue_date'],
                        book['due_date']
                    ), tags=(tag,))

    def load_all_issued_books(self):
        """Load all issued books into the all issued treeview"""
        # Clear existing items
        for item in self.all_issued_tree.get_children():
            self.all_issued_tree.delete(item)

        # Get all issued books
        success, issued_books = self.controller.issue_controller.get_all_issued_books()

        if success and issued_books:
            count = 0
            today = datetime.now().date()

            # Add overdue tag if not already defined
            try:
                self.all_issued_tree.tag_configure('overdue', background='#ffebee')  # Light red background for overdue items
            except:
                pass

            for book in issued_books:
                status = "Returned" if book['return_date'] else "Issued"
                return_date = book['return_date'] if book['return_date'] else "-"

                # Use alternating row colors
                tag = 'evenrow' if count % 2 == 0 else 'oddrow'
                tags = [tag]

                # Check if book is overdue and not returned
                if not book['return_date']:
                    due_date = datetime.strptime(book['due_date'], "%Y-%m-%d").date()
                    if today > due_date:
                        status = "Overdue"
                        tags.append('overdue')

                count += 1

                self.all_issued_tree.insert("", "end", values=(
                    book['issue_id'],
                    book['title'],
                    book['name'],
                    book['issue_date'],
                    book['due_date'],
                    return_date,
                    status
                ), tags=tuple(tags))

    def load_student_issued_books(self):
        """Load books issued to the selected student"""
        # Clear existing items
        for item in self.student_issued_tree.get_children():
            self.student_issued_tree.delete(item)

        # Get selected student
        selected_student = self.student_var.get()

        if not selected_student or selected_student not in self.student_ids:
            return

        student_id = self.student_ids[selected_student]

        # Get books issued to this student
        success, issued_books = self.controller.issue_controller.get_issued_books_by_student(student_id)

        if success and issued_books:
            count = 0
            today = datetime.now().date()

            # Add overdue tag if not already defined
            try:
                self.student_issued_tree.tag_configure('overdue', background='#ffebee')  # Light red background for overdue items
            except:
                pass

            for book in issued_books:
                status = "Returned" if book['return_date'] else "Issued"
                return_date = book['return_date'] if book['return_date'] else "-"

                # Use alternating row colors
                tag = 'evenrow' if count % 2 == 0 else 'oddrow'
                tags = [tag]

                # Check if book is overdue and not returned
                if not book['return_date']:
                    due_date = datetime.strptime(book['due_date'], "%Y-%m-%d").date()
                    if today > due_date:
                        status = "Overdue"
                        tags.append('overdue')

                count += 1

                self.student_issued_tree.insert("", "end", values=(
                    book['issue_id'],
                    book['title'],
                    book['author'],
                    book['issue_date'],
                    book['due_date'],
                    return_date,
                    status
                ), tags=tuple(tags))

    def on_book_select(self, event):
        """Handle book selection"""
        selected_item = self.book_tree.selection()

        if not selected_item:
            return

        # Get book details
        book_id = self.book_tree.item(selected_item[0], "values")[0]
        book_title = self.book_tree.item(selected_item[0], "values")[1]

        # Update selected book label
        self.selected_book_label.config(text=f"ID: {book_id} - {book_title}", fg="#27ae60")

    def on_student_select(self, event):
        """Handle student selection"""
        selected_item = self.student_tree.selection()

        if not selected_item:
            return

        # Get student details
        student_id = self.student_tree.item(selected_item[0], "values")[0]
        student_name = self.student_tree.item(selected_item[0], "values")[1]

        # Update selected student label
        self.selected_student_label.config(text=f"ID: {student_id} - {student_name}", fg="#27ae60")

    def on_issue_select(self, event):
        """Handle issue selection"""
        selected_item = self.return_tree.selection()

        if not selected_item:
            return

        # Get issue details
        issue_id = self.return_tree.item(selected_item[0], "values")[0]
        book_title = self.return_tree.item(selected_item[0], "values")[1]
        student_name = self.return_tree.item(selected_item[0], "values")[2]

        # Update selected issue label
        self.selected_issue_label.config(text=f"ID: {issue_id} - {book_title} (Issued to {student_name})", fg="#27ae60")

    def issue_book(self):
        """Issue a book to a student"""
        # Get selected book and student
        selected_book = self.book_tree.selection()
        selected_student = self.student_tree.selection()

        if not selected_book:
            self.controller.show_message("Error", "Please select a book", True)
            return

        if not selected_student:
            self.controller.show_message("Error", "Please select a student", True)
            return

        # Get IDs
        book_id = self.book_tree.item(selected_book[0], "values")[0]
        student_id = self.student_tree.item(selected_student[0], "values")[0]

        # Get days
        try:
            days = int(self.days_entry.get())
            if days <= 0:
                raise ValueError
        except ValueError:
            self.controller.show_message("Error", "Please enter a valid number of days", True)
            return

        # Issue the book
        success, message = self.controller.issue_controller.issue_book(book_id, student_id, days)

        if success:
            self.controller.show_message("Success", message)
            # Refresh data
            self.load_available_books()
            self.load_issued_books_for_return()
            self.load_all_issued_books()
            # Reset selection
            self.selected_book_label.config(text="None", fg="#e74c3c")
            self.selected_student_label.config(text="None", fg="#e74c3c")
        else:
            self.controller.show_message("Error", message, True)

    def return_book(self):
        """Return a book"""
        # Get selected issue
        selected_issue = self.return_tree.selection()

        if not selected_issue:
            self.controller.show_message("Error", "Please select a book to return", True)
            return

        # Get issue ID and other details
        issue_id = self.return_tree.item(selected_issue[0], "values")[0]
        book_title = self.return_tree.item(selected_issue[0], "values")[1]
        student_name = self.return_tree.item(selected_issue[0], "values")[2]
        due_date_str = self.return_tree.item(selected_issue[0], "values")[4]

        # Calculate fine if overdue
        today = datetime.now().date()
        due_date = datetime.strptime(due_date_str, "%Y-%m-%d").date()
        fine = 0

        if today > due_date:
            days_overdue = (today - due_date).days
            fine = days_overdue * 1  # $1 per day

            # Show fine confirmation dialog
            if not messagebox.askyesno("Confirm Return with Fine",
                                      f"This book is {days_overdue} days overdue.\n" +
                                      f"Fine amount: ${fine}\n\n" +
                                      f"Book: {book_title}\n" +
                                      f"Student: {student_name}\n\n" +
                                      "Do you want to proceed with the return?"):
                return

        # Return the book
        success, message = self.controller.issue_controller.return_book(issue_id)

        if success:
            # Show success message with fine information if applicable
            if fine > 0:
                message = f"{message}\nFine collected: ${fine}"

            self.controller.show_message("Success", message)

            # Refresh data
            self.load_available_books()
            self.load_issued_books_for_return()
            self.load_all_issued_books()
            self.load_overdue_books()  # Also refresh overdue books tab

            # Reset selection
            self.selected_issue_label.config(text="None", fg="#e74c3c")
        else:
            self.controller.show_message("Error", message, True)


class ChangePasswordFrame(BaseFrame):
    def __init__(self, parent, controller):
        super().__init__(parent, controller)

        self.create_header("Change Password")

        # Create main content
        content_frame = tk.Frame(self, bg="#f0f0f0")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Create form in the center
        form_frame = tk.Frame(content_frame, bg="#f0f0f0", padx=20, pady=20)
        form_frame.place(relx=0.5, rely=0.5, anchor="center")

        # Current password
        tk.Label(form_frame, text="Current Password:", bg="#f0f0f0", font=("Arial", 12)).grid(row=0, column=0, sticky="w", pady=10)
        self.current_password_entry = tk.Entry(form_frame, font=("Arial", 12), width=30, show="*")
        self.current_password_entry.grid(row=0, column=1, pady=10, padx=10)

        # New password
        tk.Label(form_frame, text="New Password:", bg="#f0f0f0", font=("Arial", 12)).grid(row=1, column=0, sticky="w", pady=10)
        self.new_password_entry = tk.Entry(form_frame, font=("Arial", 12), width=30, show="*")
        self.new_password_entry.grid(row=1, column=1, pady=10, padx=10)

        # Confirm new password
        tk.Label(form_frame, text="Confirm New Password:", bg="#f0f0f0", font=("Arial", 12)).grid(row=2, column=0, sticky="w", pady=10)
        self.confirm_password_entry = tk.Entry(form_frame, font=("Arial", 12), width=30, show="*")
        self.confirm_password_entry.grid(row=2, column=1, pady=10, padx=10)

        # Buttons
        buttons_frame = tk.Frame(form_frame, bg="#f0f0f0")
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        change_button = self.create_button(buttons_frame, "Change Password", self.change_password)
        change_button.pack(side="left", padx=5)

        back_button = self.create_button(buttons_frame, "Back to Dashboard",
                                         lambda: self.controller.show_frame(DashboardFrame),
                                         bg="#95a5a6")
        back_button.pack(side="left", padx=5)

        # Add a more prominent back button at the bottom of the content frame
        back_frame = tk.Frame(content_frame, bg="#f0f0f0")
        back_frame.pack(side="bottom", fill="x", pady=10)

        dashboard_button = self.create_button(
            back_frame,
            "🏠 Back to Dashboard",
            lambda: self.controller.show_frame(DashboardFrame),
            bg=COLORS["primary"],
            width=22
        )
        dashboard_button.pack(side="right", padx=10, pady=5, ipady=5)

    def refresh(self, *args, **kwargs):
        """Refresh the frame"""
        # Clear password fields
        self.current_password_entry.delete(0, tk.END)
        self.new_password_entry.delete(0, tk.END)
        self.confirm_password_entry.delete(0, tk.END)

    def change_password(self):
        """Change the admin password"""
        current_password = self.current_password_entry.get()
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()

        if not current_password or not new_password or not confirm_password:
            self.controller.show_message("Error", "Please fill in all fields", True)
            return

        if new_password != confirm_password:
            self.controller.show_message("Error", "New passwords do not match", True)
            return

        success, message = self.controller.auth_controller.change_password(
            self.controller.current_user, current_password, new_password
        )

        if success:
            self.controller.show_message("Success", message)
            # Clear fields
            self.refresh()
            # Go back to dashboard
            self.controller.show_frame(DashboardFrame)
        else:
            self.controller.show_message("Error", message, True)


# This allows the file to be run directly
if __name__ == "__main__":
    app = LibraryApp()
    app.mainloop()
