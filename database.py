import sqlite3
import os
import hashlib
from datetime import datetime, timedelta

class Database:
    def __init__(self, db_name="library.db"):
        self.db_name = db_name
        self.conn = None
        self.cursor = None
        self.initialize()
    
    def connect(self):
        """Establish connection to the database"""
        try:
            self.conn = sqlite3.connect(self.db_name)
            self.conn.row_factory = sqlite3.Row  # This enables column access by name
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None
    
    def initialize(self):
        """Initialize the database with required tables if they don't exist"""
        if not self.connect():
            return False
        
        try:
            # Create Admin table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS Admin (
                admin_id INTEGER PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL
            )
            ''')
            
            # Create Books table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS Books (
                book_id INTEGER PRIMARY KEY,
                title TEXT NOT NULL,
                author TEXT NOT NULL,
                isbn TEXT UNIQUE NOT NULL,
                quantity INTEGER NOT NULL
            )
            ''')
            
            # Create Students table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS Students (
                student_id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                roll_no TEXT UNIQUE NOT NULL,
                department TEXT NOT NULL,
                email TEXT NOT NULL
            )
            ''')
            
            # Create IssuedBooks table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS IssuedBooks (
                issue_id INTEGER PRIMARY KEY,
                book_id INTEGER NOT NULL,
                student_id INTEGER NOT NULL,
                issue_date TEXT NOT NULL,
                due_date TEXT NOT NULL,
                return_date TEXT,
                FOREIGN KEY (book_id) REFERENCES Books (book_id),
                FOREIGN KEY (student_id) REFERENCES Students (student_id)
            )
            ''')
            
            # Check if admin exists, if not create default admin
            self.cursor.execute("SELECT COUNT(*) FROM Admin")
            count = self.cursor.fetchone()[0]
            
            if count == 0:
                # Create default admin with username 'admin' and password 'admin123'
                hashed_password = hashlib.sha256('admin123'.encode()).hexdigest()
                self.cursor.execute(
                    "INSERT INTO Admin (username, password) VALUES (?, ?)",
                    ('admin', hashed_password)
                )
            
            self.conn.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"Database initialization error: {e}")
            return False
        finally:
            self.disconnect()
    
    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        try:
            if not self.connect():
                return False, "Database connection failed"
            
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            self.conn.commit()
            
            # Check if the query is a SELECT query
            if query.strip().upper().startswith("SELECT"):
                result = self.cursor.fetchall()
                return True, result
            return True, None
            
        except sqlite3.Error as e:
            return False, f"Query execution error: {e}"
        finally:
            self.disconnect()
