import tkinter as tk
from tkinter import ttk
import sys
import os
from gui import LibraryApp, COLORS

def center_window(window, width, height):
    """Center a window on the screen"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    window.geometry(f"{width}x{height}+{x}+{y}")

def show_splash_screen():
    """Show a splash screen while the application loads"""
    splash = tk.Tk()
    splash.overrideredirect(True)  # Remove window decorations

    # Set splash screen size
    width, height = 500, 300
    center_window(splash, width, height)

    # Configure splash screen appearance
    splash.configure(bg=COLORS["primary"])

    # Add logo/icon
    logo_frame = tk.Frame(splash, bg=COLORS["primary"], pady=20)
    logo_frame.pack(fill="x")

    # Use emoji as logo if no image is available
    logo_label = tk.Label(
        logo_frame,
        text="🏛️",  # Library building emoji as logo
        font=("Segoe UI", 48),
        bg=COLORS["primary"],
        fg=COLORS["white"]
    )
    logo_label.pack(anchor="center")

    # Add title
    title_label = tk.Label(
        splash,
        text="Library Management System",
        font=("Segoe UI", 22, "bold"),
        bg=COLORS["primary"],
        fg=COLORS["white"]
    )
    title_label.pack(pady=(0, 10))

    # Add loading message
    loading_label = tk.Label(
        splash,
        text="Loading...",
        font=("Segoe UI", 12),
        bg=COLORS["primary"],
        fg=COLORS["white"]
    )
    loading_label.pack(pady=10)

    # Add progress bar
    progress = ttk.Progressbar(
        splash,
        orient="horizontal",
        length=400,
        mode="indeterminate"
    )
    progress.pack(pady=20, padx=50)
    progress.start(15)

    # Add version info
    version_label = tk.Label(
        splash,
        text="Version 1.0.0",
        font=("Segoe UI", 8),
        bg=COLORS["primary"],
        fg=COLORS["medium_gray"]
    )
    version_label.pack(side="bottom", pady=10)

    # Update the splash screen
    splash.update()

    return splash

def launch_main_app(splash=None):
    """Launch the main application"""
    # Create the main application
    app = LibraryApp()

    # Configure the application for best appearance
    app.state('zoomed')  # Maximize the window

    # If splash screen exists, destroy it before showing the main app
    if splash:
        splash.destroy()

    # Start the main event loop
    app.mainloop()

if __name__ == "__main__":
    # Show splash screen
    splash = show_splash_screen()

    # Schedule the main app to launch after a short delay (1.5 seconds)
    splash.after(1500, lambda: launch_main_app(splash))

    # Start the splash screen event loop
    splash.mainloop()
