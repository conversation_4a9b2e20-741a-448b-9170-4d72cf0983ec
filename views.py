import os
from controllers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BookController, StudentController, IssueController

class View:
    def clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self, title):
        """Print a header with the given title"""
        self.clear_screen()
        print("=" * 50)
        print(f"{title:^50}")
        print("=" * 50)
        print()
    
    def print_message(self, message, success=True):
        """Print a success or error message"""
        print()
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
        print()
    
    def get_input(self, prompt):
        """Get input from the user"""
        return input(f"{prompt}: ")
    
    def get_int_input(self, prompt):
        """Get integer input from the user"""
        while True:
            try:
                return int(input(f"{prompt}: "))
            except ValueError:
                print("Please enter a valid number")
    
    def press_enter_to_continue(self):
        """Wait for the user to press Enter"""
        input("\nPress Enter to continue...")


class LoginView(View):
    def __init__(self):
        super().__init__()
        self.auth_controller = AuthController()
    
    def show_login(self):
        """Show the login screen and handle login"""
        while True:
            self.print_header("Library Management System - Login")
            
            username = self.get_input("Username")
            password = self.get_input("Password")
            
            if self.auth_controller.login(username, password):
                self.print_message("Login successful")
                self.press_enter_to_continue()
                return True, username
            else:
                self.print_message("Invalid username or password", False)
                
                retry = self.get_input("Try again? (y/n)").lower()
                if retry != 'y':
                    return False, None


class MainMenuView(View):
    def __init__(self, username):
        super().__init__()
        self.username = username
    
    def show_menu(self):
        """Show the main menu and handle user choice"""
        while True:
            self.print_header(f"Library Management System - Main Menu (Logged in as: {self.username})")
            
            print("1. Book Management")
            print("2. Student Management")
            print("3. Issue/Return Books")
            print("4. Change Password")
            print("5. Logout")
            
            choice = self.get_input("Enter your choice")
            
            if choice == '1':
                book_view = BookView()
                book_view.show_menu()
            elif choice == '2':
                student_view = StudentView()
                student_view.show_menu()
            elif choice == '3':
                issue_view = IssueView()
                issue_view.show_menu()
            elif choice == '4':
                password_view = ChangePasswordView(self.username)
                password_view.show_change_password()
            elif choice == '5':
                return
            else:
                self.print_message("Invalid choice", False)
                self.press_enter_to_continue()


class BookView(View):
    def __init__(self):
        super().__init__()
        self.book_controller = BookController()
    
    def show_menu(self):
        """Show the book management menu and handle user choice"""
        while True:
            self.print_header("Book Management")
            
            print("1. Add New Book")
            print("2. View All Books")
            print("3. Search Books")
            print("4. Update Book")
            print("5. Delete Book")
            print("6. Back to Main Menu")
            
            choice = self.get_input("Enter your choice")
            
            if choice == '1':
                self.add_book()
            elif choice == '2':
                self.view_all_books()
            elif choice == '3':
                self.search_books()
            elif choice == '4':
                self.update_book()
            elif choice == '5':
                self.delete_book()
            elif choice == '6':
                return
            else:
                self.print_message("Invalid choice", False)
                self.press_enter_to_continue()
    
    def add_book(self):
        """Handle adding a new book"""
        self.print_header("Add New Book")
        
        title = self.get_input("Title")
        author = self.get_input("Author")
        isbn = self.get_input("ISBN")
        quantity = self.get_input("Quantity")
        
        success, message = self.book_controller.add_book(title, author, isbn, quantity)
        self.print_message(message, success)
        self.press_enter_to_continue()
    
    def view_all_books(self):
        """Display all books"""
        self.print_header("All Books")
        
        success, books = self.book_controller.get_all_books()
        
        if success and books:
            print(f"{'ID':<5} {'Title':<30} {'Author':<20} {'ISBN':<15} {'Quantity':<10}")
            print("-" * 80)
            
            for book in books:
                print(f"{book['book_id']:<5} {book['title'][:28]:<30} {book['author'][:18]:<20} {book['isbn']:<15} {book['quantity']:<10}")
        elif success:
            self.print_message("No books found", False)
        else:
            self.print_message(books, False)
        
        self.press_enter_to_continue()
    
    def search_books(self):
        """Handle book search"""
        self.print_header("Search Books")
        
        search_term = self.get_input("Enter search term (title, author, or ISBN)")
        
        success, books = self.book_controller.search_books(search_term)
        
        if success and books:
            print(f"{'ID':<5} {'Title':<30} {'Author':<20} {'ISBN':<15} {'Quantity':<10}")
            print("-" * 80)
            
            for book in books:
                print(f"{book['book_id']:<5} {book['title'][:28]:<30} {book['author'][:18]:<20} {book['isbn']:<15} {book['quantity']:<10}")
        elif success:
            self.print_message("No books found", False)
        else:
            self.print_message(books, False)
        
        self.press_enter_to_continue()
    
    def update_book(self):
        """Handle updating a book"""
        self.print_header("Update Book")
        
        book_id = self.get_input("Enter Book ID")
        
        success, book = self.book_controller.get_book_by_id(book_id)
        
        if success:
            print(f"Current details: {book['title']} by {book['author']}, ISBN: {book['isbn']}, Quantity: {book['quantity']}")
            
            title = self.get_input(f"Title [{book['title']}]") or book['title']
            author = self.get_input(f"Author [{book['author']}]") or book['author']
            isbn = self.get_input(f"ISBN [{book['isbn']}]") or book['isbn']
            quantity = self.get_input(f"Quantity [{book['quantity']}]") or book['quantity']
            
            success, message = self.book_controller.update_book(book_id, title, author, isbn, quantity)
            self.print_message(message, success)
        else:
            self.print_message(book, False)
        
        self.press_enter_to_continue()
    
    def delete_book(self):
        """Handle deleting a book"""
        self.print_header("Delete Book")
        
        book_id = self.get_input("Enter Book ID")
        
        success, book = self.book_controller.get_book_by_id(book_id)
        
        if success:
            print(f"Book to delete: {book['title']} by {book['author']}, ISBN: {book['isbn']}")
            
            confirm = self.get_input("Are you sure you want to delete this book? (y/n)").lower()
            
            if confirm == 'y':
                success, message = self.book_controller.delete_book(book_id)
                self.print_message(message, success)
            else:
                self.print_message("Deletion cancelled")
        else:
            self.print_message(book, False)
        
        self.press_enter_to_continue()


class StudentView(View):
    def __init__(self):
        super().__init__()
        self.student_controller = StudentController()
    
    def show_menu(self):
        """Show the student management menu and handle user choice"""
        while True:
            self.print_header("Student Management")
            
            print("1. Add New Student")
            print("2. View All Students")
            print("3. Update Student")
            print("4. Delete Student")
            print("5. Back to Main Menu")
            
            choice = self.get_input("Enter your choice")
            
            if choice == '1':
                self.add_student()
            elif choice == '2':
                self.view_all_students()
            elif choice == '3':
                self.update_student()
            elif choice == '4':
                self.delete_student()
            elif choice == '5':
                return
            else:
                self.print_message("Invalid choice", False)
                self.press_enter_to_continue()
    
    def add_student(self):
        """Handle adding a new student"""
        self.print_header("Add New Student")
        
        name = self.get_input("Name")
        roll_no = self.get_input("Roll Number")
        department = self.get_input("Department")
        email = self.get_input("Email")
        
        success, message = self.student_controller.add_student(name, roll_no, department, email)
        self.print_message(message, success)
        self.press_enter_to_continue()
    
    def view_all_students(self):
        """Display all students"""
        self.print_header("All Students")
        
        success, students = self.student_controller.get_all_students()
        
        if success and students:
            print(f"{'ID':<5} {'Name':<25} {'Roll No':<15} {'Department':<15} {'Email':<25}")
            print("-" * 85)
            
            for student in students:
                print(f"{student['student_id']:<5} {student['name'][:23]:<25} {student['roll_no']:<15} {student['department'][:13]:<15} {student['email'][:23]:<25}")
        elif success:
            self.print_message("No students found", False)
        else:
            self.print_message(students, False)
        
        self.press_enter_to_continue()
    
    def update_student(self):
        """Handle updating a student"""
        self.print_header("Update Student")
        
        student_id = self.get_input("Enter Student ID")
        
        success, student = self.student_controller.get_student_by_id(student_id)
        
        if success:
            print(f"Current details: {student['name']}, Roll No: {student['roll_no']}, Department: {student['department']}, Email: {student['email']}")
            
            name = self.get_input(f"Name [{student['name']}]") or student['name']
            roll_no = self.get_input(f"Roll Number [{student['roll_no']}]") or student['roll_no']
            department = self.get_input(f"Department [{student['department']}]") or student['department']
            email = self.get_input(f"Email [{student['email']}]") or student['email']
            
            success, message = self.student_controller.update_student(student_id, name, roll_no, department, email)
            self.print_message(message, success)
        else:
            self.print_message(student, False)
        
        self.press_enter_to_continue()
    
    def delete_student(self):
        """Handle deleting a student"""
        self.print_header("Delete Student")
        
        student_id = self.get_input("Enter Student ID")
        
        success, student = self.student_controller.get_student_by_id(student_id)
        
        if success:
            print(f"Student to delete: {student['name']}, Roll No: {student['roll_no']}")
            
            confirm = self.get_input("Are you sure you want to delete this student? (y/n)").lower()
            
            if confirm == 'y':
                success, message = self.student_controller.delete_student(student_id)
                self.print_message(message, success)
            else:
                self.print_message("Deletion cancelled")
        else:
            self.print_message(student, False)
        
        self.press_enter_to_continue()


class IssueView(View):
    def __init__(self):
        super().__init__()
        self.issue_controller = IssueController()
        self.book_controller = BookController()
        self.student_controller = StudentController()
    
    def show_menu(self):
        """Show the issue/return menu and handle user choice"""
        while True:
            self.print_header("Issue/Return Books")
            
            print("1. Issue Book")
            print("2. Return Book")
            print("3. View All Issued Books")
            print("4. View Books Issued to a Student")
            print("5. Back to Main Menu")
            
            choice = self.get_input("Enter your choice")
            
            if choice == '1':
                self.issue_book()
            elif choice == '2':
                self.return_book()
            elif choice == '3':
                self.view_all_issued_books()
            elif choice == '4':
                self.view_issued_books_by_student()
            elif choice == '5':
                return
            else:
                self.print_message("Invalid choice", False)
                self.press_enter_to_continue()
    
    def issue_book(self):
        """Handle issuing a book to a student"""
        self.print_header("Issue Book")
        
        # Show available books
        success, books = self.book_controller.get_all_books()
        
        if success and books:
            print("Available Books:")
            print(f"{'ID':<5} {'Title':<30} {'Author':<20} {'ISBN':<15} {'Quantity':<10}")
            print("-" * 80)
            
            for book in books:
                if book['quantity'] > 0:
                    print(f"{book['book_id']:<5} {book['title'][:28]:<30} {book['author'][:18]:<20} {book['isbn']:<15} {book['quantity']:<10}")
        
        # Show students
        success, students = self.student_controller.get_all_students()
        
        if success and students:
            print("\nStudents:")
            print(f"{'ID':<5} {'Name':<25} {'Roll No':<15}")
            print("-" * 45)
            
            for student in students:
                print(f"{student['student_id']:<5} {student['name'][:23]:<25} {student['roll_no']:<15}")
        
        print()
        book_id = self.get_input("Enter Book ID")
        student_id = self.get_input("Enter Student ID")
        days = self.get_input("Enter number of days (default: 14)") or "14"
        
        success, message = self.issue_controller.issue_book(book_id, student_id, days)
        self.print_message(message, success)
        self.press_enter_to_continue()
    
    def return_book(self):
        """Handle returning a book"""
        self.print_header("Return Book")
        
        # Show issued books
        success, issued_books = self.issue_controller.get_all_issued_books()
        
        if success and issued_books:
            print("Currently Issued Books:")
            print(f"{'Issue ID':<10} {'Book Title':<30} {'Student Name':<25} {'Issue Date':<12} {'Due Date':<12}")
            print("-" * 89)
            
            for book in issued_books:
                if not book['return_date']:  # Only show books that haven't been returned
                    print(f"{book['issue_id']:<10} {book['title'][:28]:<30} {book['name'][:23]:<25} {book['issue_date']:<12} {book['due_date']:<12}")
        
        print()
        issue_id = self.get_input("Enter Issue ID")
        
        success, message = self.issue_controller.return_book(issue_id)
        self.print_message(message, success)
        self.press_enter_to_continue()
    
    def view_all_issued_books(self):
        """Display all issued books"""
        self.print_header("All Issued Books")
        
        success, issued_books = self.issue_controller.get_all_issued_books()
        
        if success and issued_books:
            print(f"{'Issue ID':<10} {'Book Title':<30} {'Student Name':<25} {'Issue Date':<12} {'Due Date':<12} {'Return Date':<12} {'Status':<10}")
            print("-" * 111)
            
            for book in issued_books:
                status = "Returned" if book['return_date'] else "Issued"
                return_date = book['return_date'] if book['return_date'] else "-"
                print(f"{book['issue_id']:<10} {book['title'][:28]:<30} {book['name'][:23]:<25} {book['issue_date']:<12} {book['due_date']:<12} {return_date:<12} {status:<10}")
        elif success:
            self.print_message("No issued books found", False)
        else:
            self.print_message(issued_books, False)
        
        self.press_enter_to_continue()
    
    def view_issued_books_by_student(self):
        """Display books issued to a specific student"""
        self.print_header("Books Issued to Student")
        
        # Show students
        success, students = self.student_controller.get_all_students()
        
        if success and students:
            print("Students:")
            print(f"{'ID':<5} {'Name':<25} {'Roll No':<15}")
            print("-" * 45)
            
            for student in students:
                print(f"{student['student_id']:<5} {student['name'][:23]:<25} {student['roll_no']:<15}")
        
        print()
        student_id = self.get_input("Enter Student ID")
        
        # Get student details
        success, student = self.student_controller.get_student_by_id(student_id)
        
        if success:
            self.print_header(f"Books Issued to {student['name']}")
            
            success, issued_books = self.issue_controller.get_issued_books_by_student(student_id)
            
            if success and issued_books:
                print(f"{'Issue ID':<10} {'Book Title':<30} {'Author':<20} {'Issue Date':<12} {'Due Date':<12} {'Return Date':<12} {'Status':<10}")
                print("-" * 106)
                
                for book in issued_books:
                    status = "Returned" if book['return_date'] else "Issued"
                    return_date = book['return_date'] if book['return_date'] else "-"
                    print(f"{book['issue_id']:<10} {book['title'][:28]:<30} {book['author'][:18]:<20} {book['issue_date']:<12} {book['due_date']:<12} {return_date:<12} {status:<10}")
            elif success:
                self.print_message("No books issued to this student", False)
            else:
                self.print_message(issued_books, False)
        else:
            self.print_message(student, False)
        
        self.press_enter_to_continue()


class ChangePasswordView(View):
    def __init__(self, username):
        super().__init__()
        self.auth_controller = AuthController()
        self.username = username
    
    def show_change_password(self):
        """Handle changing the admin password"""
        self.print_header("Change Password")
        
        old_password = self.get_input("Current Password")
        new_password = self.get_input("New Password")
        confirm_password = self.get_input("Confirm New Password")
        
        if new_password != confirm_password:
            self.print_message("Passwords do not match", False)
        else:
            success, message = self.auth_controller.change_password(self.username, old_password, new_password)
            self.print_message(message, success)
        
        self.press_enter_to_continue()
