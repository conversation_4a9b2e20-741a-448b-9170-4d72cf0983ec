"""
Build script to create a standalone executable for the Library Management System.
This script uses PyInstaller to create an executable with the lib.ico icon.
"""

import os
import subprocess
import sys

def check_pyinstaller():
    """Check if PyInstaller is installed, install if not."""
    # Get the path to pip
    pip_path = os.path.join(os.path.dirname(sys.executable), "Scripts", "pip.exe")
    if not os.path.exists(pip_path):
        pip_path = [sys.executable, "-m", "pip"]  # Fall back to python -m pip
    else:
        pip_path = [pip_path]

    try:
        # Try to import PyInstaller
        subprocess.check_call([sys.executable, "-c", "import PyInstaller"])
        print("PyInstaller is already installed.")
        return True
    except (ImportError, subprocess.CalledProcessError):
        print("PyInstaller is not installed. Installing...")
        try:
            install_cmd = pip_path + ["install", "pyinstaller"]
            subprocess.check_call(install_cmd)
            print("PyInstaller installed successfully.")
            return True
        except subprocess.CalledProcessError:
            print("Failed to install PyInstaller. Please install it manually with:")
            print(f"{' '.join(pip_path)} install pyinstaller")
            return False

def build_executable():
    """Build the executable using PyInstaller."""
    # Check if lib.ico exists
    if not os.path.exists("lib.ico"):
        print("Warning: lib.ico not found in the current directory.")
        print("The executable will use the default Python icon.")

    # Get the path to PyInstaller
    pyinstaller_path = os.path.join(os.path.dirname(sys.executable), "Scripts", "pyinstaller.exe")
    if not os.path.exists(pyinstaller_path):
        pyinstaller_path = "pyinstaller"  # Fall back to PATH if not found

    # Build command
    cmd = [
        pyinstaller_path,
        "--name=LibraryManagementSystem",
        "--windowed",  # No console window
        "--onefile",   # Single executable file
        f"--icon=lib.ico",
        "--add-data=lib.ico;.",  # Include the icon file
        "gui_main.py"  # Main script
    ]

    # Run PyInstaller
    print("Building executable...")
    try:
        subprocess.check_call(cmd)
        print("\nBuild successful!")
        print("The executable is located in the 'dist' folder.")
        print("Executable name: LibraryManagementSystem.exe")
    except subprocess.CalledProcessError as e:
        print(f"Build failed with error: {e}")
        return False

    return True

if __name__ == "__main__":
    print("Library Management System - Build Script")
    print("=======================================")

    if check_pyinstaller():
        build_executable()
