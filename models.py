import hashlib
from datetime import datetime, timedelta
from database import Database

class Admin:
    def __init__(self):
        self.db = Database()

    def authenticate(self, username, password):
        """Authenticate admin with username and password"""
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        query = "SELECT * FROM Admin WHERE username = ? AND password = ?"
        success, result = self.db.execute_query(query, (username, hashed_password))

        if success and result:
            return True
        return False

    def change_password(self, username, old_password, new_password):
        """Change admin password"""
        if not self.authenticate(username, old_password):
            return False, "Current password is incorrect"

        hashed_new_password = hashlib.sha256(new_password.encode()).hexdigest()
        query = "UPDATE Admin SET password = ? WHERE username = ?"
        success, result = self.db.execute_query(query, (hashed_new_password, username))

        if success:
            return True, "Password changed successfully"
        return False, "Failed to change password"


class Book:
    def __init__(self):
        self.db = Database()

    def add_book(self, title, author, isbn, quantity):
        """Add a new book to the database"""
        query = "INSERT INTO Books (title, author, isbn, quantity) VALUES (?, ?, ?, ?)"
        success, result = self.db.execute_query(query, (title, author, isbn, quantity))

        if success:
            return True, "Book added successfully"
        return False, "Failed to add book. ISBN might already exist."

    def get_all_books(self):
        """Get all books from the database"""
        query = "SELECT * FROM Books"
        success, result = self.db.execute_query(query)

        if success:
            return True, result
        return False, "Failed to retrieve books"

    def get_book_by_id(self, book_id):
        """Get a book by its ID"""
        query = "SELECT * FROM Books WHERE book_id = ?"
        success, result = self.db.execute_query(query, (book_id,))

        if success and result:
            return True, result[0]
        return False, "Book not found"

    def update_book(self, book_id, title, author, isbn, quantity):
        """Update book details"""
        query = """
        UPDATE Books
        SET title = ?, author = ?, isbn = ?, quantity = ?
        WHERE book_id = ?
        """
        success, result = self.db.execute_query(query, (title, author, isbn, quantity, book_id))

        if success:
            return True, "Book updated successfully"
        return False, "Failed to update book"

    def delete_book(self, book_id):
        """Delete a book from the database"""
        # First check if the book is issued to any student
        query = "SELECT * FROM IssuedBooks WHERE book_id = ? AND return_date IS NULL"
        success, result = self.db.execute_query(query, (book_id,))

        if success and result:
            return False, "Cannot delete book as it is issued to students"

        query = "DELETE FROM Books WHERE book_id = ?"
        success, result = self.db.execute_query(query, (book_id,))

        if success:
            return True, "Book deleted successfully"
        return False, "Failed to delete book"

    def search_books(self, search_term):
        """Search books by title, author, or ISBN"""
        query = """
        SELECT * FROM Books
        WHERE title LIKE ? OR author LIKE ? OR isbn LIKE ?
        """
        search_pattern = f"%{search_term}%"
        success, result = self.db.execute_query(query, (search_pattern, search_pattern, search_pattern))

        if success:
            return True, result
        return False, "Failed to search books"

    def search_books_by_isbn(self, isbn):
        """Search books specifically by ISBN"""
        query = """
        SELECT * FROM Books
        WHERE isbn LIKE ?
        """
        search_pattern = f"%{isbn}%"
        success, result = self.db.execute_query(query, (search_pattern,))

        if success:
            return True, result
        return False, "Failed to search books by ISBN"


class Student:
    def __init__(self):
        self.db = Database()

    def add_student(self, name, roll_no, department, email):
        """Add a new student to the database"""
        query = "INSERT INTO Students (name, roll_no, department, email) VALUES (?, ?, ?, ?)"
        success, result = self.db.execute_query(query, (name, roll_no, department, email))

        if success:
            return True, "Student added successfully"
        return False, "Failed to add student. Roll number might already exist."

    def get_all_students(self):
        """Get all students from the database"""
        query = "SELECT * FROM Students"
        success, result = self.db.execute_query(query)

        if success:
            return True, result
        return False, "Failed to retrieve students"

    def get_student_by_id(self, student_id):
        """Get a student by their ID"""
        query = "SELECT * FROM Students WHERE student_id = ?"
        success, result = self.db.execute_query(query, (student_id,))

        if success and result:
            return True, result[0]
        return False, "Student not found"

    def search_students_by_id(self, student_id):
        """Search students by ID or roll number"""
        query = """
        SELECT * FROM Students
        WHERE student_id LIKE ? OR roll_no LIKE ?
        """
        search_pattern = f"%{student_id}%"
        success, result = self.db.execute_query(query, (search_pattern, search_pattern))

        if success:
            return True, result
        return False, "Failed to search students by ID"

    def search_students_by_email(self, email):
        """Search students by email"""
        query = """
        SELECT * FROM Students
        WHERE email LIKE ?
        """
        search_pattern = f"%{email}%"
        success, result = self.db.execute_query(query, (search_pattern,))

        if success:
            return True, result
        return False, "Failed to search students by email"

    def update_student(self, student_id, name, roll_no, department, email):
        """Update student details"""
        query = """
        UPDATE Students
        SET name = ?, roll_no = ?, department = ?, email = ?
        WHERE student_id = ?
        """
        success, result = self.db.execute_query(query, (name, roll_no, department, email, student_id))

        if success:
            return True, "Student updated successfully"
        return False, "Failed to update student"

    def delete_student(self, student_id):
        """Delete a student from the database"""
        # First check if the student has any books issued
        query = "SELECT * FROM IssuedBooks WHERE student_id = ? AND return_date IS NULL"
        success, result = self.db.execute_query(query, (student_id,))

        if success and result:
            return False, "Cannot delete student as they have books issued"

        query = "DELETE FROM Students WHERE student_id = ?"
        success, result = self.db.execute_query(query, (student_id,))

        if success:
            return True, "Student deleted successfully"
        return False, "Failed to delete student"


class IssuedBook:
    def __init__(self):
        self.db = Database()

    def issue_book(self, book_id, student_id, days=14):
        """Issue a book to a student"""
        # Check if book exists and has available copies
        book = Book()
        success, book_data = book.get_book_by_id(book_id)

        if not success:
            return False, "Book not found"

        if book_data['quantity'] <= 0:
            return False, "No copies available for this book"

        # Check if student exists
        student = Student()
        success, student_data = student.get_student_by_id(student_id)

        if not success:
            return False, "Student not found"

        # Check if student already has this book
        query = """
        SELECT * FROM IssuedBooks
        WHERE book_id = ? AND student_id = ? AND return_date IS NULL
        """
        success, result = self.db.execute_query(query, (book_id, student_id))

        if success and result:
            return False, "This book is already issued to this student"

        # Issue the book
        issue_date = datetime.now().strftime("%Y-%m-%d")
        due_date = (datetime.now() + timedelta(days=days)).strftime("%Y-%m-%d")

        query = """
        INSERT INTO IssuedBooks (book_id, student_id, issue_date, due_date)
        VALUES (?, ?, ?, ?)
        """
        success, result = self.db.execute_query(query, (book_id, student_id, issue_date, due_date))

        if not success:
            return False, "Failed to issue book"

        # Update book quantity
        query = "UPDATE Books SET quantity = quantity - 1 WHERE book_id = ?"
        success, result = self.db.execute_query(query, (book_id,))

        if success:
            return True, "Book issued successfully"
        return False, "Failed to update book quantity"

    def return_book(self, issue_id):
        """Return a book and calculate fine if any"""
        # Check if the issue record exists
        query = "SELECT * FROM IssuedBooks WHERE issue_id = ? AND return_date IS NULL"
        success, result = self.db.execute_query(query, (issue_id,))

        if not success or not result:
            return False, "Issue record not found or book already returned"

        issue_record = result[0]
        book_id = issue_record['book_id']
        due_date = datetime.strptime(issue_record['due_date'], "%Y-%m-%d")
        today = datetime.now()

        # Calculate fine (if book is returned late)
        fine = 0
        if today > due_date:
            days_late = (today - due_date).days
            fine = days_late * 1  # $1 per day

        # Update the issue record with return date
        return_date = today.strftime("%Y-%m-%d")
        query = "UPDATE IssuedBooks SET return_date = ? WHERE issue_id = ?"
        success, result = self.db.execute_query(query, (return_date, issue_id))

        if not success:
            return False, "Failed to update return date"

        # Update book quantity
        query = "UPDATE Books SET quantity = quantity + 1 WHERE book_id = ?"
        success, result = self.db.execute_query(query, (book_id,))

        if not success:
            return False, "Failed to update book quantity"

        if fine > 0:
            return True, f"Book returned successfully. Fine: ${fine}"
        return True, "Book returned successfully"

    def get_all_issued_books(self):
        """Get all issued books with student and book details"""
        query = """
        SELECT i.issue_id, i.issue_date, i.due_date, i.return_date,
               b.book_id, b.title, b.author, b.isbn,
               s.student_id, s.name, s.roll_no, s.department
        FROM IssuedBooks i
        JOIN Books b ON i.book_id = b.book_id
        JOIN Students s ON i.student_id = s.student_id
        """
        success, result = self.db.execute_query(query)

        if success:
            return True, result
        return False, "Failed to retrieve issued books"

    def get_issued_books_by_student(self, student_id):
        """Get all books issued to a specific student"""
        query = """
        SELECT i.issue_id, i.issue_date, i.due_date, i.return_date,
               b.book_id, b.title, b.author, b.isbn
        FROM IssuedBooks i
        JOIN Books b ON i.book_id = b.book_id
        WHERE i.student_id = ?
        """
        success, result = self.db.execute_query(query, (student_id,))

        if success:
            return True, result
        return False, "Failed to retrieve issued books for this student"
